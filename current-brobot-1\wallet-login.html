<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DasWos - Wallet Login</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            max-width: 400px;
            width: 100%;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            padding: 24px;
            text-align: center;
            border-bottom: 1px solid #e5e5e5;
        }

        .logo {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 16px;
        }

        .logo-text {
            font-size: 32px;
            font-weight: bold;
            color: black;
            margin-right: 8px;
        }

        .logo-box {
            width: 32px;
            height: 32px;
            background: black;
            position: relative;
        }

        .logo-box::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 14px;
            height: 10px;
            background: white;
        }

        .logo-box::after {
            content: '';
            position: absolute;
            bottom: 2px;
            left: 2px;
            width: 8px;
            height: 8px;
            background: white;
        }

        .tagline {
            color: #666;
            font-size: 14px;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            margin: 0;
        }

        .tab-btn {
            flex: 1;
            padding: 12px 16px;
            background: transparent;
            border: none;
            color: #666;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
        }

        .tab-btn.active {
            background: white;
            color: black;
            border-bottom: 2px solid black;
            font-weight: 500;
        }

        .tab-btn:hover:not(.active) {
            background: #f0f0f0;
        }

        .form-container {
            padding: 24px;
        }

        .auth-form {
            display: none;
        }

        .auth-form.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-size: 14px;
            font-weight: 500;
        }

        .input-container {
            position: relative;
        }

        .form-group input {
            width: 100%;
            padding: 12px 40px 12px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            color: #333;
            font-size: 14px;
            transition: all 0.2s ease;
            background: white;
        }

        .form-group input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .form-group input::placeholder {
            color: #999;
        }

        .input-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            width: 16px;
            height: 16px;
        }

        .submit-btn {
            width: 100%;
            padding: 12px;
            background: black;
            border: none;
            border-radius: 6px;
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 15px;
        }

        .submit-btn:hover {
            background: #333;
        }

        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .error-message {
            color: #dc3545;
            text-align: center;
            margin-bottom: 15px;
            font-size: 14px;
            min-height: 20px;
        }

        .success-message {
            color: #28a745;
            text-align: center;
            margin-bottom: 15px;
            font-size: 14px;
            min-height: 20px;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
            padding: 16px;
            border-top: 1px solid #e5e5e5;
        }

        .back-link a {
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
        }

        .back-link a:hover {
            text-decoration: underline;
        }

        .demo-info {
            background: #f8f9fa;
            border: 1px solid #e5e5e5;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 12px;
        }

        .demo-info h4 {
            color: #333;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .demo-info p {
            color: #666;
            margin-bottom: 3px;
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }

            .container {
                max-width: 100%;
            }

            .header {
                padding: 16px;
            }

            .form-container {
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <span class="logo-text">daswos</span>
                <div class="logo-box"></div>
            </div>
            <p class="tagline">Wallet Access Portal</p>
        </div>

        <div class="tabs">
            <button class="tab-btn active" onclick="showTab('login')">Login to Wallet</button>
            <button class="tab-btn" onclick="showTab('create')">Create Wallet</button>
        </div>

        <div class="form-container">
            <!-- Demo Account Info -->
            <div class="demo-info">
                <h4>Demo Wallet</h4>
                <p><strong>Wallet ID:</strong> demo-wallet</p>
                <p><strong>Password:</strong> demo123</p>
            </div>

            <!-- Login Form -->
            <form id="loginForm" class="auth-form active">
                <div class="form-group">
                    <label for="loginWalletId">Wallet ID</label>
                    <div class="input-container">
                        <input type="text" id="loginWalletId" name="walletId" placeholder="Your wallet ID" required>
                        <svg class="input-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect width="18" height="11" x="3" y="11" rx="2" ry="2"/>
                            <path d="m7 11V7a5 5 0 0 1 10 0v4"/>
                        </svg>
                    </div>
                </div>
                <div class="form-group">
                    <label for="loginPassword">Password</label>
                    <div class="input-container">
                        <input type="password" id="loginPassword" name="password" placeholder="Your wallet password" required>
                        <svg class="input-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect width="18" height="11" x="3" y="11" rx="2" ry="2"/>
                            <path d="m7 11V7a5 5 0 0 1 10 0v4"/>
                        </svg>
                    </div>
                </div>
                <div id="loginError" class="error-message"></div>
                <div id="loginSuccess" class="success-message"></div>
                <button type="submit" class="submit-btn">Access Wallet</button>
            </form>

            <!-- Create Wallet Form -->
            <form id="createForm" class="auth-form">
                <div class="form-group">
                    <label for="createWalletId">Choose Wallet ID</label>
                    <input type="text" id="createWalletId" name="walletId" placeholder="Choose a unique wallet ID" required>
                </div>
                <div class="form-group">
                    <label for="createPassword">Password</label>
                    <input type="password" id="createPassword" name="password" placeholder="Create a secure password" required>
                </div>
                <div class="form-group">
                    <label for="createConfirmPassword">Confirm Password</label>
                    <input type="password" id="createConfirmPassword" name="confirmPassword" placeholder="Confirm your password" required>
                </div>
                <div id="createError" class="error-message"></div>
                <div id="createSuccess" class="success-message"></div>
                <button type="submit" class="submit-btn">Create Wallet</button>
            </form>
        </div>

        <div class="back-link">
            <a href="index.html">← Back to DasWos Robot</a>
        </div>
    </div>

    <script>
        // Tab switching
        function showTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Update forms
            document.querySelectorAll('.auth-form').forEach(form => form.classList.remove('active'));
            document.getElementById(tabName + 'Form').classList.add('active');

            // Clear messages
            clearMessages();
        }

        function clearMessages() {
            document.getElementById('loginError').textContent = '';
            document.getElementById('loginSuccess').textContent = '';
            document.getElementById('createError').textContent = '';
            document.getElementById('createSuccess').textContent = '';
        }

        // Wallet login functionality
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const walletId = document.getElementById('loginWalletId').value;
            const password = document.getElementById('loginPassword').value;
            const errorDiv = document.getElementById('loginError');
            const successDiv = document.getElementById('loginSuccess');

            try {
                errorDiv.textContent = '';
                successDiv.textContent = '';

                // Demo wallet check
                if (walletId === 'demo-wallet' && password === 'demo123') {
                    const walletData = {
                        wallet_id: 'demo-wallet',
                        balance: 1000,
                        created_at: new Date().toISOString()
                    };

                    // Store wallet data
                    localStorage.setItem('daswos_wallet', JSON.stringify(walletData));

                    successDiv.textContent = 'Wallet access successful! Redirecting...';
                    
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1500);
                } else {
                    errorDiv.textContent = 'Invalid wallet ID or password';
                }

            } catch (error) {
                console.error('Wallet login error:', error);
                errorDiv.textContent = 'Login failed. Please try again.';
            }
        });

        // Create wallet functionality
        document.getElementById('createForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const walletId = document.getElementById('createWalletId').value;
            const password = document.getElementById('createPassword').value;
            const confirmPassword = document.getElementById('createConfirmPassword').value;
            const errorDiv = document.getElementById('createError');
            const successDiv = document.getElementById('createSuccess');

            try {
                errorDiv.textContent = '';
                successDiv.textContent = '';

                if (password !== confirmPassword) {
                    errorDiv.textContent = 'Passwords do not match';
                    return;
                }

                if (password.length < 6) {
                    errorDiv.textContent = 'Password must be at least 6 characters';
                    return;
                }

                // Create new wallet
                const walletData = {
                    wallet_id: walletId,
                    balance: 100, // Starting balance
                    created_at: new Date().toISOString()
                };

                // Store wallet data
                localStorage.setItem('daswos_wallet', JSON.stringify(walletData));

                successDiv.textContent = 'Wallet created successfully! Redirecting...';
                
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);

            } catch (error) {
                console.error('Wallet creation error:', error);
                errorDiv.textContent = 'Failed to create wallet. Please try again.';
            }
        });
    </script>
</body>
</html>
