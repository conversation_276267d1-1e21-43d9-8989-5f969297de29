# Database Configuration - Supabase (Updated to match current-brobot-1)
DATABASE_URL=postgresql://postgres.azleuohipwepwkyoafhg:[YOUR-PASSWORD]@aws-0-eu-west-2.pooler.supabase.com:6543/postgres

# Stripe API Keys (Replace with your actual keys)
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key

# Supabase Configuration (Updated to match current-brobot-1)
SUPABASE_URL=https://azleuohipwepwkyoafhg.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF6bGV1b2hpcHdlcHdreW9hZmhnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYxMjIxNywiZXhwIjoyMDY0MTg4MjE3fQ.GoMkaJQ8rYq1mBjEPfiH1OpF4mWEyZPtxXgby5XZ-jE

# Application Settings
NODE_ENV=development
PORT=5000
