// Node.js script to create test users with proper scrypt hashes
// Run with: node create_scrypt_users.js

import crypto from 'crypto';
import { promisify } from 'util';

const scryptAsync = promisify(crypto.scrypt);

// Function to hash password using scrypt (same as daswos-18)
async function hashPassword(password) {
  const salt = crypto.randomBytes(16).toString("hex");
  const buf = await scryptAsync(password, salt, 64);
  return `${buf.toString("hex")}.${salt}`;
}

// Function to generate SQL for creating users
async function generateUserSQL() {
  console.log('🔐 Generating scrypt-hashed test users...\n');

  const users = [
    { username: 'testuser', password: 'test123', email: '<EMAIL>', fullName: 'Test User', isAdmin: false, isSeller: false },
    { username: 'demouser', password: 'demo123', email: '<EMAIL>', fullName: 'Demo User', isAdmin: false, isSeller: true },
    { username: 'adminuser', password: 'admin123', email: '<EMAIL>', fullName: 'Admin User', isAdmin: true, isSeller: true },
    { username: 'seller1', password: 'seller123', email: '<EMAIL>', fullName: 'Seller One', isAdmin: false, isSeller: true }
  ];

  let sqlScript = `-- Scrypt-hashed test users for unified authentication
-- Generated on ${new Date().toISOString()}
-- These users will work with both current-brobot-1 and daswos-18

`;

  for (const user of users) {
    const hashedPassword = await hashPassword(user.password);
    
    console.log(`✅ ${user.username} / ${user.password} -> ${hashedPassword.substring(0, 20)}...`);
    
    sqlScript += `-- User: ${user.username} (password: ${user.password})
DELETE FROM users WHERE username = '${user.username}';
INSERT INTO users (
    username, 
    password, 
    email, 
    full_name, 
    is_admin, 
    is_seller, 
    trust_score,
    daswos_coins_balance,
    identity_verified,
    identity_verification_status
) VALUES (
    '${user.username}', 
    '${hashedPassword}',
    '${user.email}', 
    '${user.fullName}', 
    ${user.isAdmin}, 
    ${user.isSeller}, 
    ${user.isSeller ? 85 : 50},
    ${user.isSeller ? 2000 : 1000},
    ${user.isSeller},
    '${user.isSeller ? 'approved' : 'none'}'
);

`;
  }

  sqlScript += `-- Verify users were created
SELECT 
    username, 
    email, 
    full_name, 
    is_admin, 
    is_seller, 
    trust_score,
    daswos_coins_balance,
    'Scrypt-hashed password' as security_note
FROM users 
WHERE username IN ('testuser', 'demouser', 'adminuser', 'seller1')
ORDER BY username;

-- Success message
SELECT 
    'SCRYPT USERS CREATED SUCCESSFULLY!' as status,
    'All users now use secure scrypt password hashing' as security,
    'Compatible with both current-brobot-1 and daswos-18' as compatibility;
`;

  console.log('\n📄 Generated SQL script:\n');
  console.log(sqlScript);
  
  // Write to file
  const fs = await import('fs');
  fs.writeFileSync('scrypt_users.sql', sqlScript);
  console.log('\n💾 SQL script saved to: scrypt_users.sql');
  
  console.log('\n🎯 Test Credentials:');
  users.forEach(user => {
    console.log(`   ${user.username} / ${user.password}`);
  });
}

// Run the script
generateUserSQL().catch(console.error);
