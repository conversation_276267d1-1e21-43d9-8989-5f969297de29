-- Migration script to convert existing SHA-256 passwords to scrypt format
-- This script will reset passwords for existing users so they can use the new scrypt system

-- First, let's see what users we have with SHA-256 hashes
SELECT 
    username, 
    email,
    CASE 
        WHEN password LIKE '%.%' THEN 'Already scrypt format'
        WHEN LENGTH(password) = 64 THEN 'SHA-256 format (needs migration)'
        ELSE 'Unknown format'
    END as password_format
FROM users
ORDER BY username;

-- For demonstration, we'll create new scrypt-hashed passwords for known users
-- In a real migration, you'd need users to reset their passwords

-- Admin user with password "admin123" (scrypt format)
-- This is a pre-generated scrypt hash for "admin123"
UPDATE users 
SET password = 'a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890.1234567890abcdef1234567890abcdef'
WHERE username = 'admin';

-- Note: The above is a placeholder hash. In reality, you would:
-- 1. Force password reset for all existing users
-- 2. Use the new scrypt hashing when they set new passwords
-- 3. Or provide a temporary migration period with dual hash support

-- Create a new test user with scrypt hash for testing
-- Password: "newtest123" 
DELETE FROM users WHERE username = 'newtest';

-- This would need to be generated using the actual scrypt function
-- For now, we'll create a placeholder that shows the format
INSERT INTO users (
    username, 
    password, 
    email, 
    full_name, 
    is_admin, 
    is_seller, 
    trust_score,
    daswos_coins_balance
) VALUES (
    'newtest', 
    'placeholder_scrypt_hash.placeholder_salt', -- This needs to be generated properly
    '<EMAIL>', 
    'New Test User', 
    false, 
    false, 
    50,
    1000
);

-- Show migration status
SELECT 
    'MIGRATION NOTICE' as status,
    'Existing users will need to reset passwords' as message,
    'New registrations will use secure scrypt hashing' as note;

-- Instructions for completing the migration
SELECT 
    'NEXT STEPS:' as step,
    '1. Users with SHA-256 passwords need to reset' as action_1,
    '2. New registrations will use scrypt automatically' as action_2,
    '3. Test with newly created accounts first' as action_3;
