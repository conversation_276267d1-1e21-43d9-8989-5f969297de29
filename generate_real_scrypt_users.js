// Node.js script to generate real scrypt hashes for test users
// Run with: node generate_real_scrypt_users.js

import crypto from 'crypto';
import { promisify } from 'util';

const scryptAsync = promisify(crypto.scrypt);

// Function to hash password using scrypt (exactly like daswos-18)
async function hashPassword(password) {
  const salt = crypto.randomBytes(16).toString("hex");
  const buf = await scryptAsync(password, salt, 64);
  return `${buf.toString("hex")}.${salt}`;
}

// Generate real scrypt hashes for test users
async function generateRealUsers() {
  console.log('🔐 Generating REAL scrypt-hashed test users...\n');

  const users = [
    { username: 'admin', password: 'admin123', email: '<EMAIL>', fullName: 'Admin User', isAdmin: true, isSeller: true, coins: 5000 },
    { username: 'testuser', password: 'test123', email: '<EMAIL>', fullName: 'Test User', isAdmin: false, isSeller: false, coins: 1000 },
    { username: 'demo', password: 'demo123', email: '<EMAIL>', fullName: 'Demo User', isAdmin: false, isSeller: true, coins: 2000 },
    { username: 'seller', password: 'seller123', email: '<EMAIL>', fullName: 'Seller User', isAdmin: false, isSeller: true, coins: 3000 }
  ];

  let sqlScript = `-- REAL scrypt-hashed test users for unified authentication
-- Generated on ${new Date().toISOString()}
-- These users will work with both current-brobot-1 and daswos-18

-- Delete existing test users first
DELETE FROM users WHERE username IN ('admin', 'testuser', 'demo', 'seller');

`;

  console.log('📝 Test Credentials:');
  for (const user of users) {
    const hashedPassword = await hashPassword(user.password);
    
    console.log(`   ${user.username} / ${user.password}`);
    
    sqlScript += `-- User: ${user.username} (password: ${user.password})
INSERT INTO users (
    username, 
    password, 
    email, 
    full_name, 
    is_admin, 
    is_seller, 
    trust_score,
    daswos_coins_balance,
    identity_verified,
    identity_verification_status
) VALUES (
    '${user.username}', 
    '${hashedPassword}',
    '${user.email}', 
    '${user.fullName}', 
    ${user.isAdmin}, 
    ${user.isSeller}, 
    ${user.isSeller ? 90 : 50},
    ${user.coins},
    ${user.isSeller},
    '${user.isSeller ? 'approved' : 'none'}'
);

`;
  }

  sqlScript += `-- Verify users were created with correct password format
SELECT 
    username, 
    email, 
    full_name, 
    is_admin, 
    is_seller, 
    trust_score,
    daswos_coins_balance,
    CASE 
        WHEN password LIKE '%.%' THEN 'Scrypt format (compatible)'
        ELSE 'Invalid format'
    END as password_format
FROM users 
WHERE username IN ('admin', 'testuser', 'demo', 'seller')
ORDER BY username;

-- Success message
SELECT 
    'REAL SCRYPT USERS CREATED SUCCESSFULLY!' as status,
    'All users now use secure scrypt password hashing' as security,
    'Compatible with both current-brobot-1 and daswos-18' as compatibility,
    'Ready for unified authentication testing' as next_step;
`;

  console.log('\n📄 Generated SQL script with real scrypt hashes:\n');
  
  // Write to file
  const fs = await import('fs');
  fs.writeFileSync('real_scrypt_users.sql', sqlScript);
  console.log('💾 SQL script saved to: real_scrypt_users.sql');
  
  console.log('\n🎯 Instructions:');
  console.log('1. Run this SQL script in your Supabase SQL Editor');
  console.log('2. Test login in both apps with any of the credentials above');
  console.log('3. Users created in either app will work in both apps');
}

// Run the script
generateRealUsers().catch(console.error);
