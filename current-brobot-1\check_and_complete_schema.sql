-- CHECK WHAT EXISTS AND COMPLETE THE SCHEMA

-- Step 1: Check what tables already exist
SELECT 'Existing tables:' as info;
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;

-- Step 2: Check what data exists
SELECT 'Checking existing data:' as info;

-- Check users
SELECT 'Users table:' as table_name, COUNT(*) as count FROM users;
SELECT username, email, is_admin FROM users LIMIT 5;

-- Check categories (if exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'categories') THEN
        RAISE NOTICE 'Categories table exists';
    ELSE
        RAISE NOTICE 'Categories table does not exist';
    END IF;
END $$;

-- Check products (if exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'products') THEN
        RAISE NOTICE 'Products table exists';
    ELSE
        RAISE NOTICE 'Products table does not exist';
    END IF;
END $$;

-- Step 3: Create missing tables only if they don't exist

-- Create categories if missing
CREATE TABLE IF NOT EXISTS categories (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    slug TEXT UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create products if missing
CREATE TABLE IF NOT EXISTS products (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    price INTEGER NOT NULL,
    image_url TEXT,
    seller_id INTEGER NOT NULL,
    seller_name TEXT NOT NULL,
    category_id INTEGER,
    tags TEXT[] DEFAULT '{}',
    in_stock BOOLEAN DEFAULT true,
    trust_score INTEGER DEFAULT 30,
    status TEXT DEFAULT 'active',
    quantity INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_sessions if missing
CREATE TABLE IF NOT EXISTS user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    session_token TEXT NOT NULL UNIQUE,
    device_info JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Create cart_items if missing
CREATE TABLE IF NOT EXISTS cart_items (
    id SERIAL PRIMARY KEY,
    user_id INTEGER,
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 4: Add missing columns to users table if needed
DO $$
BEGIN
    -- Add is_seller column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'is_seller') THEN
        ALTER TABLE users ADD COLUMN is_seller BOOLEAN DEFAULT false;
    END IF;
    
    -- Add trust_score column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'trust_score') THEN
        ALTER TABLE users ADD COLUMN trust_score INTEGER DEFAULT 30;
    END IF;
END $$;

-- Step 5: Add foreign keys if they don't exist
DO $$
BEGIN
    -- Add products foreign keys
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'fk_products_seller') THEN
        ALTER TABLE products ADD CONSTRAINT fk_products_seller FOREIGN KEY (seller_id) REFERENCES users(id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'fk_products_category') THEN
        ALTER TABLE products ADD CONSTRAINT fk_products_category FOREIGN KEY (category_id) REFERENCES categories(id);
    END IF;
    
    -- Add user_sessions foreign key
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'fk_user_sessions_user') THEN
        ALTER TABLE user_sessions ADD CONSTRAINT fk_user_sessions_user FOREIGN KEY (user_id) REFERENCES users(id);
    END IF;
    
    -- Add cart_items foreign keys
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'fk_cart_items_user') THEN
        ALTER TABLE cart_items ADD CONSTRAINT fk_cart_items_user FOREIGN KEY (user_id) REFERENCES users(id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'fk_cart_items_product') THEN
        ALTER TABLE cart_items ADD CONSTRAINT fk_cart_items_product FOREIGN KEY (product_id) REFERENCES products(id);
    END IF;
END $$;

-- Step 6: Insert sample data if missing

-- Insert admin user if not exists
INSERT INTO users (username, password, email, full_name, is_admin, is_seller, trust_score) 
VALUES ('admin', '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9', '<EMAIL>', 'Admin User', true, true, 100)
ON CONFLICT (username) DO NOTHING;

-- Insert categories if empty
INSERT INTO categories (name, slug, description) 
SELECT * FROM (VALUES
    ('Electronics', 'electronics', 'Electronic devices and accessories'),
    ('Clothing', 'clothing', 'Apparel and fashion items'),
    ('Sports', 'sports', 'Sports equipment and gear')
) AS v(name, slug, description)
WHERE NOT EXISTS (SELECT 1 FROM categories LIMIT 1);

-- Insert products if empty
INSERT INTO products (title, description, price, image_url, seller_id, seller_name, category_id, tags, trust_score) 
SELECT * FROM (VALUES
    ('Wireless Headphones', 'High-quality wireless headphones with noise cancellation', 19999, 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop', 1, 'admin', 1, '{"electronics", "audio", "wireless"}', 85),
    ('Running Shoes', 'Lightweight running shoes for all terrains', 8999, 'https://images.unsplash.com/photo-**********-7eec264c27ff?w=400&h=400&fit=crop', 1, 'admin', 3, '{"footwear", "sports", "running"}', 85),
    ('Smart Watch', 'Feature-rich smartwatch with health tracking', 24999, 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop', 1, 'admin', 1, '{"electronics", "wearables", "fitness"}', 85)
) AS v(title, description, price, image_url, seller_id, seller_name, category_id, tags, trust_score)
WHERE NOT EXISTS (SELECT 1 FROM products LIMIT 1);

-- Step 7: Final verification
SELECT 'SCHEMA COMPLETION SUCCESSFUL!' as status;
SELECT 
    (SELECT COUNT(*) FROM users) as users_count,
    (SELECT COUNT(*) FROM categories) as categories_count,
    (SELECT COUNT(*) FROM products) as products_count,
    (SELECT COUNT(*) FROM user_sessions) as sessions_count,
    (SELECT COUNT(*) FROM cart_items) as cart_items_count;

-- Show all tables
SELECT 'All tables:' as info;
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;
