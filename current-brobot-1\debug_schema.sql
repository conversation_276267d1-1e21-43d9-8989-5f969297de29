-- DEBUG SCHEMA CREATION
-- Let's create tables one by one to see where it fails

-- Step 1: Check current state
SELECT 'Current tables:' as step;
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;

-- Step 2: Try creating just the users table
SELECT 'Creating users table...' as step;
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    email TEXT NOT NULL UNIQUE,
    full_name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_admin BOOLEAN DEFAULT false
);

-- Step 3: Check if users table was created
SELECT 'Checking users table...' as step;
SELECT table_name FROM information_schema.tables WHERE table_name = 'users';

-- Step 4: Try inserting admin user
SELECT 'Inserting admin user...' as step;
INSERT INTO users (username, password, email, full_name, is_admin) 
VALUES ('admin', '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9', '<EMAIL>', 'Admin User', true);

-- Step 5: Check if user was inserted
SELECT 'Checking admin user...' as step;
SELECT username, email, is_admin FROM users WHERE username = 'admin';

-- Step 6: Try creating categories table
SELECT 'Creating categories table...' as step;
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    slug TEXT UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 7: Insert categories
SELECT 'Inserting categories...' as step;
INSERT INTO categories (name, slug, description) VALUES
    ('Electronics', 'electronics', 'Electronic devices'),
    ('Sports', 'sports', 'Sports equipment');

-- Step 8: Try creating products table
SELECT 'Creating products table...' as step;
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    price INTEGER NOT NULL,
    image_url TEXT,
    seller_id INTEGER NOT NULL,
    seller_name TEXT NOT NULL,
    category_id INTEGER,
    tags TEXT[],
    in_stock BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 9: Add foreign key
SELECT 'Adding foreign key...' as step;
ALTER TABLE products ADD CONSTRAINT fk_products_seller FOREIGN KEY (seller_id) REFERENCES users(id);

-- Step 10: Insert sample product
SELECT 'Inserting sample product...' as step;
INSERT INTO products (title, description, price, image_url, seller_id, seller_name, category_id, tags) 
VALUES ('Test Product', 'A test product', 1999, 'https://via.placeholder.com/400', 1, 'admin', 1, '{"test"}');

-- Step 11: Final verification
SELECT 'Final verification...' as step;
SELECT 
    (SELECT COUNT(*) FROM users) as users_count,
    (SELECT COUNT(*) FROM categories) as categories_count,
    (SELECT COUNT(*) FROM products) as products_count;

SELECT 'All tables created:' as step;
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;
