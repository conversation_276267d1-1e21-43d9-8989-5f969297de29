<!DOCTYPE html>
<html>
<head>
    <title>Database Setup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Database Setup for Daswos Robot App</h1>

    <div id="status" class="status info">Ready to setup database...</div>

    <button onclick="checkDatabase()">1. Check Current Database</button>
    <button onclick="setupCategories()">2. Setup Categories</button>
    <button onclick="setupProducts()">3. Setup Products (5 items)</button>
    <button onclick="setupMoreProducts()">3b. Add More Products (20 items)</button>
    <button onclick="testConnection()">4. Test Final Connection</button>

    <div id="results"></div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        const SUPABASE_URL = 'https://nzgajyfnijjfsvdmidqd.supabase.co';
        const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56Z2FqeWZuaWpqZnN2ZG1pZHFkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYwMDUzNSwiZXhwIjoyMDY0MTc2NTM1fQ.XK2clfmvi5JI5NhQuCrz-WChn-ZvRW9VY2e3gVtNwK4';

        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_KEY);

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML += `
                <div class="status ${type}">
                    <h3>${title}</h3>
                    <pre>${content}</pre>
                </div>
            `;
        }

        async function checkDatabase() {
            updateStatus('Checking database...', 'info');

            try {
                // Check categories
                const { data: categories, error: catError } = await supabase
                    .from('categories')
                    .select('*');

                // Check products
                const { data: products, error: prodError } = await supabase
                    .from('products')
                    .select('*');

                let result = `Categories: ${categories?.length || 0} found\n`;
                result += `Products: ${products?.length || 0} found\n\n`;

                if (catError) result += `Categories Error: ${catError.message}\n`;
                if (prodError) result += `Products Error: ${prodError.message}\n`;

                if (categories) result += `\nCategories:\n${JSON.stringify(categories, null, 2)}\n`;
                if (products) result += `\nProducts:\n${JSON.stringify(products, null, 2)}`;

                addResult('Database Check Results', result, catError || prodError ? 'error' : 'success');
                updateStatus('Database check completed', 'success');

            } catch (error) {
                addResult('Database Check Error', error.message, 'error');
                updateStatus('Database check failed', 'error');
            }
        }

        async function setupCategories() {
            updateStatus('Setting up categories...', 'info');

            try {
                const categories = [
                    { name: 'Electronics', slug: 'electronics', description: 'Electronic devices and accessories' },
                    { name: 'Clothing', slug: 'clothing', description: 'Apparel and fashion items' },
                    { name: 'Home & Garden', slug: 'home-garden', description: 'Home improvement and gardening supplies' },
                    { name: 'Sports', slug: 'sports', description: 'Sports equipment and gear' },
                    { name: 'Toys', slug: 'toys', description: 'Toys and games' }
                ];

                const { data, error } = await supabase
                    .from('categories')
                    .insert(categories)
                    .select();

                if (error) throw error;

                addResult('Categories Setup', `Successfully created ${data.length} categories:\n${JSON.stringify(data, null, 2)}`, 'success');
                updateStatus('Categories setup completed', 'success');

            } catch (error) {
                addResult('Categories Setup Error', error.message, 'error');
                updateStatus('Categories setup failed', 'error');
            }
        }

        async function setupProducts() {
            updateStatus('Setting up products...', 'info');

            try {
                // First get category IDs
                const { data: categories } = await supabase
                    .from('categories')
                    .select('id, slug');

                if (!categories || categories.length === 0) {
                    throw new Error('No categories found. Please setup categories first.');
                }

                const catMap = {};
                categories.forEach(cat => catMap[cat.slug] = cat.id);

                const products = [
                    {
                        title: 'Wireless Headphones',
                        description: 'High-quality wireless headphones with noise cancellation',
                        price: 199.99,
                        image_url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
                        category_id: catMap['electronics'],
                        tags: ['electronics', 'audio', 'wireless'],
                        in_stock: true
                    },
                    {
                        title: 'Running Shoes',
                        description: 'Lightweight running shoes for all terrains',
                        price: 89.99,
                        image_url: 'https://images.unsplash.com/photo-**********-7eec264c27ff?w=400&h=400&fit=crop',
                        category_id: catMap['sports'],
                        tags: ['footwear', 'sports', 'running'],
                        in_stock: true
                    },
                    {
                        title: 'Smart Watch',
                        description: 'Feature-rich smartwatch with health tracking',
                        price: 249.99,
                        image_url: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
                        category_id: catMap['electronics'],
                        tags: ['electronics', 'wearables', 'fitness'],
                        in_stock: true
                    },
                    {
                        title: 'Yoga Mat',
                        description: 'Non-slip yoga mat for all skill levels',
                        price: 29.99,
                        image_url: 'https://images.unsplash.com/photo-1576678927484-cc907957088c?w=400&h=400&fit=crop',
                        category_id: catMap['sports'],
                        tags: ['fitness', 'yoga', 'home'],
                        in_stock: true
                    },
                    {
                        title: 'Blender',
                        description: 'High-powered blender for smoothies and food prep',
                        price: 129.99,
                        image_url: 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400&h=400&fit=crop',
                        category_id: catMap['home-garden'],
                        tags: ['kitchen', 'appliances'],
                        in_stock: true
                    }
                ];

                const { data, error } = await supabase
                    .from('products')
                    .insert(products)
                    .select();

                if (error) throw error;

                addResult('Products Setup', `Successfully created ${data.length} products:\n${JSON.stringify(data, null, 2)}`, 'success');
                updateStatus('Products setup completed', 'success');

            } catch (error) {
                addResult('Products Setup Error', error.message, 'error');
                updateStatus('Products setup failed', 'error');
            }
        }

        async function setupMoreProducts() {
            updateStatus('Setting up additional products...', 'info');

            try {
                // First get category IDs
                const { data: categories } = await supabase
                    .from('categories')
                    .select('id, slug');

                if (!categories || categories.length === 0) {
                    throw new Error('No categories found. Please setup categories first.');
                }

                const catMap = {};
                categories.forEach(cat => catMap[cat.slug] = cat.id);

                const moreProducts = [
                    {
                        title: 'Gaming Keyboard',
                        description: 'Mechanical gaming keyboard with RGB lighting',
                        price: 149.99,
                        image_url: 'https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=400&h=400&fit=crop',
                        category_id: catMap['electronics'],
                        tags: ['gaming', 'keyboard', 'rgb'],
                        in_stock: true
                    },
                    {
                        title: 'Wireless Mouse',
                        description: 'Ergonomic wireless mouse with precision tracking',
                        price: 79.99,
                        image_url: 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop',
                        category_id: catMap['electronics'],
                        tags: ['mouse', 'wireless', 'ergonomic'],
                        in_stock: true
                    },
                    {
                        title: 'Coffee Maker',
                        description: 'Programmable coffee maker with thermal carafe',
                        price: 199.99,
                        image_url: 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=400&h=400&fit=crop',
                        category_id: catMap['home-garden'],
                        tags: ['coffee', 'kitchen', 'appliance'],
                        in_stock: true
                    },
                    {
                        title: 'Desk Lamp',
                        description: 'LED desk lamp with adjustable brightness',
                        price: 59.99,
                        image_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop',
                        category_id: catMap['home-garden'],
                        tags: ['lamp', 'led', 'desk'],
                        in_stock: true
                    },
                    {
                        title: 'Basketball',
                        description: 'Official size basketball for indoor/outdoor play',
                        price: 39.99,
                        image_url: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?w=400&h=400&fit=crop',
                        category_id: catMap['sports'],
                        tags: ['basketball', 'sports', 'outdoor'],
                        in_stock: true
                    },
                    {
                        title: 'Backpack',
                        description: 'Durable hiking backpack with multiple compartments',
                        price: 89.99,
                        image_url: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop',
                        category_id: catMap['sports'],
                        tags: ['backpack', 'hiking', 'outdoor'],
                        in_stock: true
                    },
                    {
                        title: 'T-Shirt',
                        description: 'Comfortable cotton t-shirt in various colors',
                        price: 24.99,
                        image_url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop',
                        category_id: catMap['clothing'],
                        tags: ['shirt', 'cotton', 'casual'],
                        in_stock: true
                    },
                    {
                        title: 'Jeans',
                        description: 'Classic blue jeans with comfortable fit',
                        price: 79.99,
                        image_url: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=400&h=400&fit=crop',
                        category_id: catMap['clothing'],
                        tags: ['jeans', 'denim', 'casual'],
                        in_stock: true
                    },
                    {
                        title: 'Sneakers',
                        description: 'Comfortable sneakers for everyday wear',
                        price: 119.99,
                        image_url: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=400&fit=crop',
                        category_id: catMap['clothing'],
                        tags: ['shoes', 'sneakers', 'casual'],
                        in_stock: true
                    },
                    {
                        title: 'Tablet',
                        description: '10-inch tablet with high-resolution display',
                        price: 329.99,
                        image_url: 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=400&fit=crop',
                        category_id: catMap['electronics'],
                        tags: ['tablet', 'mobile', 'display'],
                        in_stock: true
                    },
                    {
                        title: 'Water Bottle',
                        description: 'Insulated stainless steel water bottle',
                        price: 34.99,
                        image_url: 'https://images.unsplash.com/photo-1602143407151-7111542de6e8?w=400&h=400&fit=crop',
                        category_id: catMap['sports'],
                        tags: ['bottle', 'water', 'insulated'],
                        in_stock: true
                    },
                    {
                        title: 'Sunglasses',
                        description: 'UV protection sunglasses with polarized lenses',
                        price: 149.99,
                        image_url: 'https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=400&h=400&fit=crop',
                        category_id: catMap['clothing'],
                        tags: ['sunglasses', 'uv', 'polarized'],
                        in_stock: true
                    },
                    {
                        title: 'Plant Pot',
                        description: 'Ceramic plant pot with drainage holes',
                        price: 29.99,
                        image_url: 'https://images.unsplash.com/photo-1485955900006-10f4d324d411?w=400&h=400&fit=crop',
                        category_id: catMap['home-garden'],
                        tags: ['pot', 'plant', 'ceramic'],
                        in_stock: true
                    },
                    {
                        title: 'Phone Case',
                        description: 'Protective phone case with shock absorption',
                        price: 19.99,
                        image_url: 'https://images.unsplash.com/photo-1556656793-08538906a9f8?w=400&h=400&fit=crop',
                        category_id: catMap['electronics'],
                        tags: ['case', 'phone', 'protection'],
                        in_stock: true
                    },
                    {
                        title: 'Puzzle Game',
                        description: '1000-piece jigsaw puzzle with beautiful artwork',
                        price: 24.99,
                        image_url: 'https://images.unsplash.com/photo-1606092195730-5d7b9af1efc5?w=400&h=400&fit=crop',
                        category_id: catMap['toys'],
                        tags: ['puzzle', 'game', 'family'],
                        in_stock: true
                    }
                ];

                const { data, error } = await supabase
                    .from('products')
                    .insert(moreProducts)
                    .select();

                if (error) throw error;

                addResult('Additional Products Setup', `Successfully created ${data.length} additional products:\n${JSON.stringify(data.slice(0, 3), null, 2)}\n... and ${data.length - 3} more`, 'success');
                updateStatus('Additional products setup completed', 'success');

            } catch (error) {
                addResult('Additional Products Setup Error', error.message, 'error');
                updateStatus('Additional products setup failed', 'error');
            }
        }

        async function testConnection() {
            updateStatus('Testing final connection...', 'info');

            try {
                const { data: products, error } = await supabase
                    .from('products')
                    .select('*, categories(name)')
                    .limit(5);

                if (error) throw error;

                addResult('Final Test Results', `Found ${products.length} products with categories:\n${JSON.stringify(products, null, 2)}`, 'success');
                updateStatus('Connection test successful! Your app should now work.', 'success');

            } catch (error) {
                addResult('Connection Test Error', error.message, 'error');
                updateStatus('Connection test failed', 'error');
            }
        }
    </script>
</body>
</html>
