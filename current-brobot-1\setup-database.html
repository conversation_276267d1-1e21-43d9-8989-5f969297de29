<!DOCTYPE html>
<html>
<head>
    <title>Database Setup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Database Setup for Daswos Robot App</h1>
    
    <div id="status" class="status info">Ready to setup database...</div>
    
    <button onclick="checkDatabase()">1. Check Current Database</button>
    <button onclick="setupCategories()">2. Setup Categories</button>
    <button onclick="setupProducts()">3. Setup Products</button>
    <button onclick="testConnection()">4. Test Final Connection</button>
    
    <div id="results"></div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        const SUPABASE_URL = 'https://nzgajyfnijjfsvdmidqd.supabase.co';
        const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56Z2FqeWZuaWpqZnN2ZG1pZHFkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYwMDUzNSwiZXhwIjoyMDY0MTc2NTM1fQ.XK2clfmvi5JI5NhQuCrz-WChn-ZvRW9VY2e3gVtNwK4';
        
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_KEY);
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML += `
                <div class="status ${type}">
                    <h3>${title}</h3>
                    <pre>${content}</pre>
                </div>
            `;
        }
        
        async function checkDatabase() {
            updateStatus('Checking database...', 'info');
            
            try {
                // Check categories
                const { data: categories, error: catError } = await supabase
                    .from('categories')
                    .select('*');
                
                // Check products
                const { data: products, error: prodError } = await supabase
                    .from('products')
                    .select('*');
                
                let result = `Categories: ${categories?.length || 0} found\n`;
                result += `Products: ${products?.length || 0} found\n\n`;
                
                if (catError) result += `Categories Error: ${catError.message}\n`;
                if (prodError) result += `Products Error: ${prodError.message}\n`;
                
                if (categories) result += `\nCategories:\n${JSON.stringify(categories, null, 2)}\n`;
                if (products) result += `\nProducts:\n${JSON.stringify(products, null, 2)}`;
                
                addResult('Database Check Results', result, catError || prodError ? 'error' : 'success');
                updateStatus('Database check completed', 'success');
                
            } catch (error) {
                addResult('Database Check Error', error.message, 'error');
                updateStatus('Database check failed', 'error');
            }
        }
        
        async function setupCategories() {
            updateStatus('Setting up categories...', 'info');
            
            try {
                const categories = [
                    { name: 'Electronics', slug: 'electronics', description: 'Electronic devices and accessories' },
                    { name: 'Clothing', slug: 'clothing', description: 'Apparel and fashion items' },
                    { name: 'Home & Garden', slug: 'home-garden', description: 'Home improvement and gardening supplies' },
                    { name: 'Sports', slug: 'sports', description: 'Sports equipment and gear' },
                    { name: 'Toys', slug: 'toys', description: 'Toys and games' }
                ];
                
                const { data, error } = await supabase
                    .from('categories')
                    .insert(categories)
                    .select();
                
                if (error) throw error;
                
                addResult('Categories Setup', `Successfully created ${data.length} categories:\n${JSON.stringify(data, null, 2)}`, 'success');
                updateStatus('Categories setup completed', 'success');
                
            } catch (error) {
                addResult('Categories Setup Error', error.message, 'error');
                updateStatus('Categories setup failed', 'error');
            }
        }
        
        async function setupProducts() {
            updateStatus('Setting up products...', 'info');
            
            try {
                // First get category IDs
                const { data: categories } = await supabase
                    .from('categories')
                    .select('id, slug');
                
                if (!categories || categories.length === 0) {
                    throw new Error('No categories found. Please setup categories first.');
                }
                
                const catMap = {};
                categories.forEach(cat => catMap[cat.slug] = cat.id);
                
                const products = [
                    {
                        title: 'Wireless Headphones',
                        description: 'High-quality wireless headphones with noise cancellation',
                        price: 199.99,
                        image_url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
                        category_id: catMap['electronics'],
                        tags: ['electronics', 'audio', 'wireless'],
                        in_stock: true
                    },
                    {
                        title: 'Running Shoes',
                        description: 'Lightweight running shoes for all terrains',
                        price: 89.99,
                        image_url: 'https://images.unsplash.com/photo-**********-7eec264c27ff?w=400&h=400&fit=crop',
                        category_id: catMap['sports'],
                        tags: ['footwear', 'sports', 'running'],
                        in_stock: true
                    },
                    {
                        title: 'Smart Watch',
                        description: 'Feature-rich smartwatch with health tracking',
                        price: 249.99,
                        image_url: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
                        category_id: catMap['electronics'],
                        tags: ['electronics', 'wearables', 'fitness'],
                        in_stock: true
                    },
                    {
                        title: 'Yoga Mat',
                        description: 'Non-slip yoga mat for all skill levels',
                        price: 29.99,
                        image_url: 'https://images.unsplash.com/photo-1576678927484-cc907957088c?w=400&h=400&fit=crop',
                        category_id: catMap['sports'],
                        tags: ['fitness', 'yoga', 'home'],
                        in_stock: true
                    },
                    {
                        title: 'Blender',
                        description: 'High-powered blender for smoothies and food prep',
                        price: 129.99,
                        image_url: 'https://images.unsplash.com/photo-1560343090-f0409ee7e33a?w=400&h=400&fit=crop',
                        category_id: catMap['home-garden'],
                        tags: ['kitchen', 'appliances'],
                        in_stock: true
                    }
                ];
                
                const { data, error } = await supabase
                    .from('products')
                    .insert(products)
                    .select();
                
                if (error) throw error;
                
                addResult('Products Setup', `Successfully created ${data.length} products:\n${JSON.stringify(data, null, 2)}`, 'success');
                updateStatus('Products setup completed', 'success');
                
            } catch (error) {
                addResult('Products Setup Error', error.message, 'error');
                updateStatus('Products setup failed', 'error');
            }
        }
        
        async function testConnection() {
            updateStatus('Testing final connection...', 'info');
            
            try {
                const { data: products, error } = await supabase
                    .from('products')
                    .select('*, categories(name)')
                    .limit(5);
                
                if (error) throw error;
                
                addResult('Final Test Results', `Found ${products.length} products with categories:\n${JSON.stringify(products, null, 2)}`, 'success');
                updateStatus('Connection test successful! Your app should now work.', 'success');
                
            } catch (error) {
                addResult('Connection Test Error', error.message, 'error');
                updateStatus('Connection test failed', 'error');
            }
        }
    </script>
</body>
</html>
