import React, { useState, useEffect, createContext, useContext } from 'react';
import { apiRequest } from '@/lib/queryClient';

interface Wallet {
  id: string;
  wallet_id: string;
  balance: number;
  created_at: string;
  last_accessed: string;
}

interface WalletSession {
  wallet: Wallet;
  session_token: string;
  expires_at: string;
}

interface WalletContextType {
  wallet: Wallet | null;
  isLoading: boolean;
  loginToWallet: (walletId: string, password: string) => Promise<{ success: boolean; error?: string }>;
  createWallet: (walletId: string, password: string) => Promise<{ success: boolean; error?: string }>;
  logoutWallet: () => void;
  refreshWallet: () => Promise<void>;
  isWalletConnected: boolean;
}

const WalletContext = createContext<WalletContextType | undefined>(undefined);

export function useWallet() {
  const context = useContext(WalletContext);
  if (!context) {
    // Return a default implementation when not in context
    return useWalletImplementation();
  }
  return context;
}

function useWalletImplementation(): WalletContextType {
  const [wallet, setWallet] = useState<Wallet | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Check for existing wallet session on mount
  useEffect(() => {
    checkExistingWalletSession();
  }, []);

  const checkExistingWalletSession = async () => {
    try {
      const storedWallet = localStorage.getItem('daswos_wallet');
      const storedSession = localStorage.getItem('daswos_wallet_session');

      if (storedWallet && storedSession) {
        const walletData = JSON.parse(storedWallet);
        const sessionData = JSON.parse(storedSession);

        // Check if session is still valid
        if (new Date(sessionData.expires_at) > new Date()) {
          setWallet(walletData);
          console.log('Restored wallet session:', walletData.wallet_id);
        } else {
          // Session expired, clear data
          localStorage.removeItem('daswos_wallet');
          localStorage.removeItem('daswos_wallet_session');
        }
      }
    } catch (error) {
      console.error('Error checking wallet session:', error);
      // Clear invalid data
      localStorage.removeItem('daswos_wallet');
      localStorage.removeItem('daswos_wallet_session');
    }
  };

  const loginToWallet = async (walletId: string, password: string): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true);

    try {
      // For demo purposes, check demo wallet
      if (walletId === 'demo-wallet' && password === 'demo123') {
        const demoWallet: Wallet = {
          id: 'demo-wallet-uuid',
          wallet_id: 'demo-wallet',
          balance: 1000,
          created_at: new Date().toISOString(),
          last_accessed: new Date().toISOString()
        };

        const sessionData = {
          wallet: demoWallet,
          session_token: 'demo-session-' + Date.now(),
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
        };

        // Store wallet data
        localStorage.setItem('daswos_wallet', JSON.stringify(demoWallet));
        localStorage.setItem('daswos_wallet_session', JSON.stringify(sessionData));

        setWallet(demoWallet);
        return { success: true };
      }

      // TODO: Replace with actual API call to wallet database
      // const response = await apiRequest('/api/wallet/login', {
      //   method: 'POST',
      //   body: JSON.stringify({ walletId, password })
      // });

      // For now, return error for non-demo wallets
      return { success: false, error: 'Invalid wallet ID or password' };

    } catch (error) {
      console.error('Wallet login error:', error);
      return { success: false, error: 'Login failed. Please try again.' };
    } finally {
      setIsLoading(false);
    }
  };

  const createWallet = async (walletId: string, password: string): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true);

    try {
      // For demo purposes, create a local wallet
      const newWallet: Wallet = {
        id: 'wallet-' + Date.now(),
        wallet_id: walletId,
        balance: 100, // Starting balance
        created_at: new Date().toISOString(),
        last_accessed: new Date().toISOString()
      };

      const sessionData = {
        wallet: newWallet,
        session_token: 'session-' + Date.now(),
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
      };

      // Store wallet data
      localStorage.setItem('daswos_wallet', JSON.stringify(newWallet));
      localStorage.setItem('daswos_wallet_session', JSON.stringify(sessionData));

      setWallet(newWallet);

      // TODO: Replace with actual API call to wallet database
      // const response = await apiRequest('/api/wallet/create', {
      //   method: 'POST',
      //   body: JSON.stringify({ walletId, password })
      // });

      return { success: true };

    } catch (error) {
      console.error('Wallet creation error:', error);
      return { success: false, error: 'Failed to create wallet. Please try again.' };
    } finally {
      setIsLoading(false);
    }
  };

  const logoutWallet = () => {
    // Clear stored wallet data
    localStorage.removeItem('daswos_wallet');
    localStorage.removeItem('daswos_wallet_session');

    // Reset wallet state
    setWallet(null);

    console.log('Wallet logged out');
  };

  const refreshWallet = async () => {
    if (!wallet) return;

    try {
      // TODO: Replace with actual API call to refresh wallet data
      // const response = await apiRequest(`/api/wallet/${wallet.id}`);
      // setWallet(response.wallet);

      console.log('Wallet refreshed');
    } catch (error) {
      console.error('Error refreshing wallet:', error);
    }
  };

  return {
    wallet,
    isLoading,
    loginToWallet,
    createWallet,
    logoutWallet,
    refreshWallet,
    isWalletConnected: !!wallet
  };
}

// Wallet Provider Component (for when we want to use context)
export function WalletProvider({ children }: { children: React.ReactNode }) {
  const walletImplementation = useWalletImplementation();

  return (
    <WalletContext.Provider value={walletImplementation}>
      {children}
    </WalletContext.Provider>
  );
}

// Utility functions for wallet operations
export const walletUtils = {
  formatBalance: (balance: number): string => {
    return balance.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  },

  validateWalletId: (walletId: string): { valid: boolean; error?: string } => {
    if (!walletId || walletId.length < 3) {
      return { valid: false, error: 'Wallet ID must be at least 3 characters' };
    }
    if (walletId.length > 50) {
      return { valid: false, error: 'Wallet ID must be less than 50 characters' };
    }
    if (!/^[a-zA-Z0-9_-]+$/.test(walletId)) {
      return { valid: false, error: 'Wallet ID can only contain letters, numbers, hyphens, and underscores' };
    }
    return { valid: true };
  },

  validatePassword: (password: string): { valid: boolean; error?: string } => {
    if (!password || password.length < 6) {
      return { valid: false, error: 'Password must be at least 6 characters' };
    }
    if (password.length > 128) {
      return { valid: false, error: 'Password must be less than 128 characters' };
    }
    return { valid: true };
  }
};

export type { Wallet, WalletSession, WalletContextType };
