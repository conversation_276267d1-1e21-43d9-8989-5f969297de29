# 🤖 Daswos Robot - Interactive Product Search App

An interactive robot application that connects to Supabase to search and display products with floating animations and clickable product details.

## 🚀 Quick Start

### Prerequisites
- Node.js (version 12 or higher)
- npm (comes with Node.js)
- A modern web browser (Chrome, Firefox, Safari, Edge)

### Installation & Setup

1. **Navigate to the project directory:**
   ```bash
   cd current-brobot-1
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Start the development server:**
   ```bash
   npm start
   ```

4. **Open your browser and visit:**
   ```
   http://localhost:8080
   ```

## 🎮 How to Use

### Basic Robot Controls
- **Click & Drag**: Move the robot around the screen
- **Hold Mouse on Robot**: Spin the robot
- **Mouse Near Robot**: Changes robot view angles

### Control Buttons
- **Idle**: Set robot to idle state
- **Talk**: Activate communication mode
- **Dance**: Start dance sequence
- **Roll**: Move robot to random position
- **Search**: Enable search mode
- **Center**: Return robot to center
- **Clear Products**: Remove all floating search results

### Product Search
1. **Type in the search box**: Enter product names (e.g., "headphones", "shoes", "watch")
2. **Press Enter or click Execute**: Search the database
3. **View Results**: Products will float around the screen as animated images
4. **Click Floating Products**: View detailed product information in popup
5. **Multiple Searches**: Search results accumulate - use "Clear Products" to remove them
6. **Stable Display**: Products maintain consistent positions and properties (supports 20+ products)

### Robot Size Control
- Use the size slider or +/- buttons to adjust robot scale
- Reset button returns to default size

## 🗄️ Database Setup

The app connects to a Supabase database. If you need to set up the database:

1. **Open the database setup tool:**
   ```
   http://localhost:8080/setup-database.html
   ```

2. **Follow these steps in order:**
   - Click "1. Check Current Database" to see existing data
   - Click "2. Setup Categories" to create product categories
   - Click "3. Setup Products (5 items)" to add initial sample products
   - Click "3b. Add More Products (20 items)" to test with many products
   - Click "4. Test Final Connection" to verify everything works

3. **Test the connection:**
   ```
   http://localhost:8080/test-supabase.html
   ```

4. **Fix broken product images (if needed):**
   ```
   http://localhost:8080/fix-images.html
   ```

## 🎯 Features

### Interactive Robot
- **5 Different Views**: Front, side, three-quarter, back, and top views
- **Smooth Animations**: Bobbing, spinning, rolling, and dancing
- **Mouse Tracking**: Robot follows mouse movement and changes views
- **State Management**: Idle, talk, dance, roll, and search modes

### Product Search & Display
- **Real-time Search**: Search products from Supabase database
- **Floating Animation**: Products float around with physics-based movement
- **Stable Display**: Products maintain consistent positions (no random repositioning)
- **Scalable Performance**: Supports up to 20 floating products simultaneously
- **Image Loading**: Automatic image loading with fallbacks
- **Product Details**: Click products for detailed popups
- **Persistent Results**: Search results remain until manually cleared
- **Duplicate Prevention**: Same products won't be added multiple times

### Smart UI Interaction
- **Non-intrusive**: Robot doesn't move when using search bar or UI controls
- **Responsive Design**: Works on desktop and mobile devices
- **Visual Feedback**: Loading states, error messages, and status updates

## 🛠️ Technical Details

### Built With
- **Frontend**: HTML5, CSS3, JavaScript
- **Animation**: p5.js library
- **Database**: Supabase (PostgreSQL)
- **Server**: Node.js with Express

### File Structure
```
current-brobot-1/
├── index.html              # Main application
├── package.json            # Dependencies and scripts
├── server.js              # Express server
├── supabase.js            # Database configuration
├── supabase_schema.sql    # Database schema
├── setup-database.html    # Database setup tool
├── test-supabase.html     # Connection test tool
├── attached_assets/       # Robot images
│   ├── robot_front_view.png
│   ├── robot_side_view.png
│   ├── robot_three_quarter_view.png
│   ├── robot_back_view.png
│   └── robot_top_view.png
└── README.md             # This file
```

### Database Schema
- **Categories Table**: Product categories (Electronics, Clothing, Sports, etc.)
- **Products Table**: Product details (title, description, price, image_url, category)

## 🔧 Troubleshooting

### Common Issues

**Robot images not loading:**
- Check that all files in `attached_assets/` folder exist
- Ensure the server is running on the correct port

**Products not appearing:**
- Run the database setup tool at `/setup-database.html`
- Check browser console for error messages
- Verify Supabase connection at `/test-supabase.html`

**Product images not loading:**
- Use the image fix tool at `/fix-images.html`
- Check for 404 errors in browser console
- Verify image URLs are accessible
- Tool will automatically replace broken images

**Search not working:**
- Ensure database has products (use setup tool)
- Check network connection
- Look for error messages in browser console

**Robot not responding to clicks:**
- Make sure you're clicking in the canvas area (not on UI elements)
- Try clicking away from the UI controls at the top
- Check that JavaScript is enabled in your browser

### Performance Tips
- Close other browser tabs for better performance
- Use a modern browser for optimal experience
- Clear products regularly to maintain smooth animation

## 🎨 Customization

### Adding New Products
Use the database setup tool or add directly to Supabase:
- Navigate to `/setup-database.html`
- Or modify the `supabase_schema.sql` file

### Changing Robot Behavior
Edit the animation parameters in `index.html`:
- Robot speed, scale, interaction distances
- Animation timing and effects
- Mouse interaction sensitivity

## 📝 License

This project is for educational and demonstration purposes.

## 🤝 Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify all dependencies are installed
3. Ensure the server is running on the correct port
4. Test the database connection using the provided tools

---

**Enjoy exploring with Daswos Robot! 🤖✨**
