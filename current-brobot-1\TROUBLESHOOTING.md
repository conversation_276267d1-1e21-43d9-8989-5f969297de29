# 🔍 Database Setup Troubleshooting

## Issue: "Success but no tables created"

This usually means the SQL executed without errors, but the tables weren't actually created. Let's diagnose this step by step.

## Step 1: Verify Current State

**Run this in Supabase SQL Editor:**
```sql
-- Copy and paste contents of verify_database.sql
```

This will show you:
- What tables exist (if any)
- What constraints exist
- What functions exist
- What extensions are enabled

## Step 2: Try Simple Creation

**Run this in Supabase SQL Editor:**
```sql
-- Copy and paste contents of create_tables_simple.sql
```

This simplified version creates only the essential tables with basic structure.

## Step 3: Check Supabase Connection

### Verify you're in the right project:
1. Go to Supabase Dashboard
2. Check project name/URL matches your app
3. Verify you're in the SQL Editor (not API docs)

### Check permissions:
1. Make sure you're using the service role key (not anon key)
2. Verify RLS (Row Level Security) isn't blocking operations

## Step 4: Manual Table Creation

If automated scripts aren't working, create tables manually:

### Create Users Table:
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    email TEXT NOT NULL UNIQUE,
    full_name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_admin BOOLEAN DEFAULT false
);
```

### Create Categories Table:
```sql
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    slug TEXT UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Create Products Table:
```sql
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    price INTEGER NOT NULL,
    image_url TEXT,
    category_id INTEGER,
    tags TEXT[],
    in_stock BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Create User Sessions Table:
```sql
CREATE TABLE user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    session_token TEXT NOT NULL UNIQUE,
    device_info JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);
```

### Create Cart Items Table:
```sql
CREATE TABLE cart_items (
    id SERIAL PRIMARY KEY,
    user_id INTEGER,
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Step 5: Add Sample Data

### Insert Admin User:
```sql
INSERT INTO users (username, password, email, full_name, is_admin) 
VALUES ('admin', '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9', '<EMAIL>', 'Admin User', true);
```

### Insert Categories:
```sql
INSERT INTO categories (name, slug, description) VALUES
    ('Electronics', 'electronics', 'Electronic devices'),
    ('Clothing', 'clothing', 'Apparel items'),
    ('Sports', 'sports', 'Sports equipment');
```

### Insert Sample Products:
```sql
INSERT INTO products (title, description, price, image_url, category_id, tags) VALUES
    ('Wireless Headphones', 'High-quality headphones', 19999, 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400', 1, '{"electronics"}'),
    ('Running Shoes', 'Lightweight shoes', 8999, 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400', 3, '{"sports"}');
```

## Step 6: Test the App

1. Open: http://localhost:8080
2. Click "Login"
3. Use: admin / admin123
4. Check if products load

## Common Issues & Solutions

### Issue: "relation does not exist"
**Solution:** Tables weren't created. Try manual creation above.

### Issue: "permission denied"
**Solution:** Check you're using the service role key, not anon key.

### Issue: "syntax error"
**Solution:** Copy-paste might have formatting issues. Try manual creation.

### Issue: "constraint already exists"
**Solution:** Some parts were created. Run cleanup_database.sql first.

### Issue: Login doesn't work
**Solution:** Check if users table exists and admin user was inserted.

### Issue: Products don't load
**Solution:** Check if products table exists and has data.

## Quick Test Queries

### Check if tables exist:
```sql
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';
```

### Check if admin user exists:
```sql
SELECT username, email FROM users WHERE username = 'admin';
```

### Check if products exist:
```sql
SELECT title, price FROM products LIMIT 5;
```

### Check if categories exist:
```sql
SELECT name FROM categories;
```

## Success Indicators

✅ Tables show up in Supabase Table Editor  
✅ Admin user login works (admin/admin123)  
✅ Products display in the app  
✅ Search functionality works  
✅ Background products float automatically  

If you're still having issues, try the manual table creation approach - it's more reliable than large SQL scripts.
