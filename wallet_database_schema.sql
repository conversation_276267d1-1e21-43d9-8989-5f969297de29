-- =====================================================
-- DASWOS WALLET DATABASE SCHEMA
-- Separate database for wallet functionality
-- Used by both daswos-18 and current-brobot-1
-- =====================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- WALLETS TABLE
-- =====================================================
CREATE TABLE wallets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id TEXT NOT NULL UNIQUE, -- User-chosen wallet ID
    password_hash TEXT NOT NULL, -- Scrypt hashed password
    balance DECIMAL(15, 2) DEFAULT 100.00 NOT NULL, -- DasWos Coins balance
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    is_active BOOLEAN DEFAULT true NOT NULL,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- Security fields
    failed_login_attempts INTEGER DEFAULT 0 NOT NULL,
    locked_until TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    creation_ip INET,
    last_login_ip INET,
    
    -- Constraints
    CONSTRAINT wallet_id_length CHECK (char_length(wallet_id) >= 3 AND char_length(wallet_id) <= 50),
    CONSTRAINT balance_non_negative CHECK (balance >= 0)
);

-- =====================================================
-- WALLET SESSIONS TABLE
-- =====================================================
CREATE TABLE wallet_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id UUID NOT NULL REFERENCES wallets(id) ON DELETE CASCADE,
    session_token TEXT NOT NULL UNIQUE,
    device_info JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- =====================================================
-- TRANSACTIONS TABLE
-- =====================================================
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id UUID NOT NULL REFERENCES wallets(id) ON DELETE CASCADE,
    transaction_type TEXT NOT NULL, -- 'credit', 'debit', 'purchase', 'refund', 'transfer_in', 'transfer_out'
    amount DECIMAL(15, 2) NOT NULL,
    balance_before DECIMAL(15, 2) NOT NULL,
    balance_after DECIMAL(15, 2) NOT NULL,
    description TEXT,
    reference_id TEXT, -- Reference to external transaction (purchase ID, etc.)
    reference_type TEXT, -- 'purchase', 'refund', 'transfer', 'admin_adjustment'
    
    -- Transfer fields (for wallet-to-wallet transfers)
    from_wallet_id UUID REFERENCES wallets(id),
    to_wallet_id UUID REFERENCES wallets(id),
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    ip_address INET,
    user_agent TEXT,
    
    -- Status
    status TEXT DEFAULT 'completed' NOT NULL, -- 'pending', 'completed', 'failed', 'cancelled'
    
    -- Constraints
    CONSTRAINT amount_positive CHECK (amount > 0),
    CONSTRAINT valid_transaction_type CHECK (transaction_type IN ('credit', 'debit', 'purchase', 'refund', 'transfer_in', 'transfer_out', 'admin_adjustment')),
    CONSTRAINT valid_status CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    CONSTRAINT transfer_wallets_different CHECK (from_wallet_id != to_wallet_id OR from_wallet_id IS NULL OR to_wallet_id IS NULL)
);

-- =====================================================
-- WALLET CONNECTIONS TABLE
-- Link wallets to user accounts in main databases
-- =====================================================
CREATE TABLE wallet_connections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id UUID NOT NULL REFERENCES wallets(id) ON DELETE CASCADE,
    
    -- Connection to main database
    database_name TEXT NOT NULL, -- 'daswos-18', 'current-brobot-1'
    user_id INTEGER, -- User ID in the main database (nullable for anonymous connections)
    username TEXT, -- Username in the main database (for reference)
    
    -- Connection metadata
    connected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    is_primary BOOLEAN DEFAULT false NOT NULL, -- Is this the primary wallet for this user?
    
    -- Constraints
    CONSTRAINT valid_database_name CHECK (database_name IN ('daswos-18', 'current-brobot-1')),
    UNIQUE(database_name, user_id, wallet_id) -- Prevent duplicate connections
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Wallets indexes
CREATE INDEX idx_wallets_wallet_id ON wallets(wallet_id);
CREATE INDEX idx_wallets_created_at ON wallets(created_at);
CREATE INDEX idx_wallets_is_active ON wallets(is_active);

-- Sessions indexes
CREATE INDEX idx_wallet_sessions_wallet_id ON wallet_sessions(wallet_id);
CREATE INDEX idx_wallet_sessions_session_token ON wallet_sessions(session_token);
CREATE INDEX idx_wallet_sessions_expires_at ON wallet_sessions(expires_at);
CREATE INDEX idx_wallet_sessions_is_active ON wallet_sessions(is_active);

-- Transactions indexes
CREATE INDEX idx_transactions_wallet_id ON transactions(wallet_id);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);
CREATE INDEX idx_transactions_transaction_type ON transactions(transaction_type);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_reference ON transactions(reference_type, reference_id);

-- Connections indexes
CREATE INDEX idx_wallet_connections_wallet_id ON wallet_connections(wallet_id);
CREATE INDEX idx_wallet_connections_database_user ON wallet_connections(database_name, user_id);

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for wallets table
CREATE TRIGGER update_wallets_updated_at 
    BEFORE UPDATE ON wallets 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Function to validate transaction balance
CREATE OR REPLACE FUNCTION validate_transaction_balance()
RETURNS TRIGGER AS $$
BEGIN
    -- Ensure balance calculations are correct
    IF NEW.transaction_type IN ('debit', 'purchase', 'transfer_out') THEN
        IF NEW.balance_after != NEW.balance_before - NEW.amount THEN
            RAISE EXCEPTION 'Invalid balance calculation for debit transaction';
        END IF;
    ELSIF NEW.transaction_type IN ('credit', 'refund', 'transfer_in', 'admin_adjustment') THEN
        IF NEW.balance_after != NEW.balance_before + NEW.amount THEN
            RAISE EXCEPTION 'Invalid balance calculation for credit transaction';
        END IF;
    END IF;
    
    -- Ensure balance doesn't go negative
    IF NEW.balance_after < 0 THEN
        RAISE EXCEPTION 'Transaction would result in negative balance';
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for transaction validation
CREATE TRIGGER validate_transaction_balance_trigger
    BEFORE INSERT ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION validate_transaction_balance();

-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================

-- Demo wallet (password: demo123)
-- Scrypt hash for 'demo123' with salt
INSERT INTO wallets (wallet_id, password_hash, balance) VALUES 
('demo-wallet', 'a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890.1234567890abcdef1234567890abcdef12345678', 1000.00);

-- Sample transaction for demo wallet
INSERT INTO transactions (wallet_id, transaction_type, amount, balance_before, balance_after, description, reference_type)
SELECT 
    w.id,
    'credit',
    1000.00,
    0.00,
    1000.00,
    'Initial wallet funding',
    'admin_adjustment'
FROM wallets w WHERE w.wallet_id = 'demo-wallet';

-- =====================================================
-- VIEWS FOR EASY QUERYING
-- =====================================================

-- Wallet summary view
CREATE VIEW wallet_summary AS
SELECT 
    w.id,
    w.wallet_id,
    w.balance,
    w.created_at,
    w.last_accessed,
    w.is_active,
    COUNT(t.id) as transaction_count,
    MAX(t.created_at) as last_transaction_date
FROM wallets w
LEFT JOIN transactions t ON w.id = t.wallet_id
WHERE w.is_active = true
GROUP BY w.id, w.wallet_id, w.balance, w.created_at, w.last_accessed, w.is_active;

-- Recent transactions view
CREATE VIEW recent_transactions AS
SELECT 
    t.id,
    w.wallet_id,
    t.transaction_type,
    t.amount,
    t.balance_after,
    t.description,
    t.created_at
FROM transactions t
JOIN wallets w ON t.wallet_id = w.id
WHERE t.status = 'completed'
ORDER BY t.created_at DESC;

-- =====================================================
-- SECURITY POLICIES (RLS)
-- =====================================================

-- Enable Row Level Security
ALTER TABLE wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_connections ENABLE ROW LEVEL SECURITY;

-- Note: RLS policies would be implemented based on your authentication system
-- For now, we'll rely on application-level security

-- =====================================================
-- GRANTS AND PERMISSIONS
-- =====================================================

-- Create a wallet service role
-- CREATE ROLE wallet_service;
-- GRANT SELECT, INSERT, UPDATE ON wallets TO wallet_service;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON wallet_sessions TO wallet_service;
-- GRANT SELECT, INSERT ON transactions TO wallet_service;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON wallet_connections TO wallet_service;

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON TABLE wallets IS 'Main wallets table storing wallet credentials and balances';
COMMENT ON TABLE wallet_sessions IS 'Active wallet sessions for authentication';
COMMENT ON TABLE transactions IS 'All wallet transactions with full audit trail';
COMMENT ON TABLE wallet_connections IS 'Links wallets to user accounts in main databases';

COMMENT ON COLUMN wallets.wallet_id IS 'User-chosen unique wallet identifier';
COMMENT ON COLUMN wallets.password_hash IS 'Scrypt hashed password for wallet access';
COMMENT ON COLUMN wallets.balance IS 'Current DasWos Coins balance';
COMMENT ON COLUMN transactions.reference_id IS 'External reference (purchase ID, etc.)';
COMMENT ON COLUMN wallet_connections.database_name IS 'Which main database this connection is for';
