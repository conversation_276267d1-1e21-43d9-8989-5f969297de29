<!DOCTYPE html>
<html>
<head>
    <title>Setup Unified Database Schema</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #4ecdc4; }
        .status { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 15px 25px; margin: 10px; cursor: pointer; background: #4ecdc4; color: #000; border: none; border-radius: 5px; font-weight: bold; }
        button:hover { background: #fff; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; color: #000; }
        h1 { color: #fff; text-align: center; }
        .container { max-width: 800px; margin: 0 auto; }
        .step { margin: 20px 0; padding: 20px; border: 1px solid #4ecdc4; border-radius: 10px; }
        .step h3 { color: #fff; margin-top: 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ Setup Unified Database Schema</h1>
        <p>This tool will set up the unified schema that works with both DasWos-18 and Current-Brobot-1 apps.</p>
        
        <div id="status" class="status info">Ready to setup unified database schema...</div>
        
        <div class="step">
            <h3>Step 1: Check Current Database</h3>
            <p>Check what tables currently exist in your database.</p>
            <button onclick="checkCurrentDatabase()">Check Current Database</button>
        </div>
        
        <div class="step">
            <h3>Step 2: Apply Unified Schema</h3>
            <p>Apply the unified schema that includes all tables from both apps.</p>
            <button onclick="applyUnifiedSchema()">Apply Unified Schema</button>
        </div>
        
        <div class="step">
            <h3>Step 3: Migrate Existing Data</h3>
            <p>Migrate any existing product data to the new schema format.</p>
            <button onclick="migrateExistingData()">Migrate Existing Data</button>
        </div>
        
        <div class="step">
            <h3>Step 4: Test Unified Database</h3>
            <p>Test that both apps can access the unified database.</p>
            <button onclick="testUnifiedDatabase()">Test Unified Database</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        const SUPABASE_URL = 'https://nzgajyfnijjfsvdmidqd.supabase.co';
        const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56Z2FqeWZuaWpqZnN2ZG1pZHFkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYwMDUzNSwiZXhwIjoyMDY0MTc2NTM1fQ.XK2clfmvi5JI5NhQuCrz-WChn-ZvRW9VY2e3gVtNwK4';
        
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_KEY);
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML += `
                <div class="status ${type}">
                    <h3>${title}</h3>
                    <div>${content}</div>
                </div>
            `;
        }
        
        async function checkCurrentDatabase() {
            updateStatus('Checking current database structure...', 'info');
            
            try {
                // Check for existing tables
                const { data: tables, error } = await supabase.rpc('get_table_names');
                
                if (error) {
                    // Fallback: try to query known tables
                    const tableChecks = [
                        { name: 'users', query: supabase.from('users').select('count', { count: 'exact', head: true }) },
                        { name: 'products', query: supabase.from('products').select('count', { count: 'exact', head: true }) },
                        { name: 'categories', query: supabase.from('categories').select('count', { count: 'exact', head: true }) },
                        { name: 'user_sessions', query: supabase.from('user_sessions').select('count', { count: 'exact', head: true }) },
                        { name: 'cart_items', query: supabase.from('cart_items').select('count', { count: 'exact', head: true }) }
                    ];
                    
                    let existingTables = [];
                    let tableCounts = {};
                    
                    for (const check of tableChecks) {
                        try {
                            const { data, error } = await check.query;
                            if (!error) {
                                existingTables.push(check.name);
                                tableCounts[check.name] = data || 0;
                            }
                        } catch (e) {
                            // Table doesn't exist
                        }
                    }
                    
                    let resultHtml = '<h4>Existing Tables:</h4><ul>';
                    if (existingTables.length > 0) {
                        existingTables.forEach(table => {
                            resultHtml += `<li><strong>${table}</strong> (${tableCounts[table]} records)</li>`;
                        });
                    } else {
                        resultHtml += '<li>No tables found or database is empty</li>';
                    }
                    resultHtml += '</ul>';
                    
                    addResult('Database Structure Check', resultHtml, 'info');
                    updateStatus(`Found ${existingTables.length} existing tables`, 'success');
                } else {
                    addResult('Database Tables', `Found tables: ${JSON.stringify(tables, null, 2)}`, 'success');
                    updateStatus('Database structure check completed', 'success');
                }
                
            } catch (error) {
                addResult('Database Check Error', error.message, 'error');
                updateStatus('Database check failed', 'error');
            }
        }
        
        async function applyUnifiedSchema() {
            updateStatus('Applying unified schema...', 'info');
            
            try {
                // Note: In a real implementation, you would execute the SQL from unified_schema.sql
                // For this demo, we'll create the essential tables manually
                
                const schemaOperations = [
                    {
                        name: 'Create Users Table',
                        operation: async () => {
                            // This would typically be done via SQL execution
                            // For demo purposes, we'll check if we can create a test user
                            const { data, error } = await supabase
                                .from('users')
                                .select('id')
                                .limit(1);
                            return { success: !error, error };
                        }
                    },
                    {
                        name: 'Create Products Table',
                        operation: async () => {
                            const { data, error } = await supabase
                                .from('products')
                                .select('id')
                                .limit(1);
                            return { success: !error, error };
                        }
                    },
                    {
                        name: 'Create Categories Table',
                        operation: async () => {
                            const { data, error } = await supabase
                                .from('categories')
                                .select('id')
                                .limit(1);
                            return { success: !error, error };
                        }
                    }
                ];
                
                let successCount = 0;
                let resultHtml = '<h4>Schema Application Results:</h4><ul>';
                
                for (const op of schemaOperations) {
                    try {
                        const result = await op.operation();
                        if (result.success) {
                            resultHtml += `<li>✅ ${op.name} - OK</li>`;
                            successCount++;
                        } else {
                            resultHtml += `<li>❌ ${op.name} - ${result.error?.message || 'Failed'}</li>`;
                        }
                    } catch (error) {
                        resultHtml += `<li>❌ ${op.name} - ${error.message}</li>`;
                    }
                }
                
                resultHtml += '</ul>';
                resultHtml += `<p><strong>Note:</strong> To fully apply the unified schema, execute the SQL from <code>unified_schema.sql</code> in your Supabase SQL editor.</p>`;
                
                addResult('Schema Application', resultHtml, successCount > 0 ? 'success' : 'warning');
                updateStatus(`Schema check completed - ${successCount}/${schemaOperations.length} tables verified`, 'success');
                
            } catch (error) {
                addResult('Schema Application Error', error.message, 'error');
                updateStatus('Schema application failed', 'error');
            }
        }
        
        async function migrateExistingData() {
            updateStatus('Migrating existing data...', 'info');
            
            try {
                // Check for existing products and migrate if needed
                const { data: products, error: productsError } = await supabase
                    .from('products')
                    .select('*')
                    .limit(10);
                
                if (productsError) {
                    throw productsError;
                }
                
                let migrationResults = '<h4>Data Migration Results:</h4>';
                
                if (products && products.length > 0) {
                    migrationResults += `<p>✅ Found ${products.length} existing products</p>`;
                    
                    // Check if products have required fields for unified schema
                    const sampleProduct = products[0];
                    const requiredFields = ['seller_id', 'seller_name', 'trust_score'];
                    const missingFields = requiredFields.filter(field => !(field in sampleProduct));
                    
                    if (missingFields.length > 0) {
                        migrationResults += `<p>⚠️ Products missing fields: ${missingFields.join(', ')}</p>`;
                        migrationResults += `<p>Manual migration may be required.</p>`;
                    } else {
                        migrationResults += `<p>✅ Products appear to be compatible with unified schema</p>`;
                    }
                } else {
                    migrationResults += `<p>ℹ️ No existing products found - fresh start</p>`;
                }
                
                addResult('Data Migration', migrationResults, 'success');
                updateStatus('Data migration check completed', 'success');
                
            } catch (error) {
                addResult('Data Migration Error', error.message, 'error');
                updateStatus('Data migration failed', 'error');
            }
        }
        
        async function testUnifiedDatabase() {
            updateStatus('Testing unified database...', 'info');
            
            try {
                const tests = [
                    {
                        name: 'Test User Authentication',
                        test: async () => {
                            const { data, error } = await supabase
                                .from('users')
                                .select('username, email')
                                .eq('username', 'admin')
                                .limit(1);
                            return { success: !error && data && data.length > 0, data, error };
                        }
                    },
                    {
                        name: 'Test Product Search',
                        test: async () => {
                            const { data, error } = await supabase
                                .from('products')
                                .select('*, categories(name)')
                                .limit(5);
                            return { success: !error, data, error };
                        }
                    },
                    {
                        name: 'Test Session Management',
                        test: async () => {
                            const { data, error } = await supabase
                                .from('user_sessions')
                                .select('count', { count: 'exact', head: true });
                            return { success: !error, data, error };
                        }
                    }
                ];
                
                let testResults = '<h4>Database Test Results:</h4><ul>';
                let passedTests = 0;
                
                for (const test of tests) {
                    try {
                        const result = await test.test();
                        if (result.success) {
                            testResults += `<li>✅ ${test.name} - PASSED</li>`;
                            passedTests++;
                        } else {
                            testResults += `<li>❌ ${test.name} - FAILED: ${result.error?.message || 'Unknown error'}</li>`;
                        }
                    } catch (error) {
                        testResults += `<li>❌ ${test.name} - ERROR: ${error.message}</li>`;
                    }
                }
                
                testResults += '</ul>';
                testResults += `<p><strong>Tests Passed: ${passedTests}/${tests.length}</strong></p>`;
                
                if (passedTests === tests.length) {
                    testResults += '<p>🎉 All tests passed! Your unified database is ready for both apps.</p>';
                }
                
                addResult('Database Tests', testResults, passedTests === tests.length ? 'success' : 'warning');
                updateStatus(`Database testing completed - ${passedTests}/${tests.length} tests passed`, 
                           passedTests === tests.length ? 'success' : 'warning');
                
            } catch (error) {
                addResult('Database Test Error', error.message, 'error');
                updateStatus('Database testing failed', 'error');
            }
        }
    </script>
</body>
</html>
