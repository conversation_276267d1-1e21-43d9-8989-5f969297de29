-- MINIMAL WORKING SCHEMA
-- Just the essential tables for the app to work

-- Users table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    email TEXT NOT NULL UNIQUE,
    full_name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_admin BOOLEAN DEFAULT false,
    is_seller BOOLEAN DEFAULT false,
    trust_score INTEGER DEFAULT 30
);

-- Categories table
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    slug TEXT UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products table
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    price INTEGER NOT NULL,
    image_url TEXT,
    seller_id INTEGER NOT NULL,
    seller_name TEXT NOT NULL,
    category_id INTEGER,
    tags TEXT[] DEFAULT '{}',
    in_stock BOOLEAN DEFAULT true,
    trust_score INTEGER DEFAULT 30,
    status TEXT DEFAULT 'active',
    quantity INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User sessions table
CREATE TABLE user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    session_token TEXT NOT NULL UNIQUE,
    device_info JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Cart items table
CREATE TABLE cart_items (
    id SERIAL PRIMARY KEY,
    user_id INTEGER,
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add foreign keys
ALTER TABLE products ADD CONSTRAINT fk_products_seller FOREIGN KEY (seller_id) REFERENCES users(id);
ALTER TABLE products ADD CONSTRAINT fk_products_category FOREIGN KEY (category_id) REFERENCES categories(id);
ALTER TABLE user_sessions ADD CONSTRAINT fk_user_sessions_user FOREIGN KEY (user_id) REFERENCES users(id);
ALTER TABLE cart_items ADD CONSTRAINT fk_cart_items_user FOREIGN KEY (user_id) REFERENCES users(id);
ALTER TABLE cart_items ADD CONSTRAINT fk_cart_items_product FOREIGN KEY (product_id) REFERENCES products(id);

-- Insert admin user
INSERT INTO users (username, password, email, full_name, is_admin, is_seller, trust_score) 
VALUES ('admin', '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9', '<EMAIL>', 'Admin User', true, true, 100);

-- Insert categories
INSERT INTO categories (name, slug, description) VALUES
    ('Electronics', 'electronics', 'Electronic devices and accessories'),
    ('Clothing', 'clothing', 'Apparel and fashion items'),
    ('Sports', 'sports', 'Sports equipment and gear');

-- Insert sample products
INSERT INTO products (title, description, price, image_url, seller_id, seller_name, category_id, tags, trust_score) VALUES
    ('Wireless Headphones', 'High-quality wireless headphones with noise cancellation', 19999, 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop', 1, 'admin', 1, '{"electronics", "audio", "wireless"}', 85),
    ('Running Shoes', 'Lightweight running shoes for all terrains', 8999, 'https://images.unsplash.com/photo-**********-7eec264c27ff?w=400&h=400&fit=crop', 1, 'admin', 3, '{"footwear", "sports", "running"}', 85),
    ('Smart Watch', 'Feature-rich smartwatch with health tracking', 24999, 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop', 1, 'admin', 1, '{"electronics", "wearables", "fitness"}', 85);

-- Verification
SELECT 'MINIMAL SCHEMA COMPLETE!' as status;
SELECT 
    (SELECT COUNT(*) FROM users) as users_count,
    (SELECT COUNT(*) FROM categories) as categories_count,
    (SELECT COUNT(*) FROM products) as products_count,
    (SELECT COUNT(*) FROM user_sessions) as sessions_count,
    (SELECT COUNT(*) FROM cart_items) as cart_items_count;

-- Show created tables
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;
