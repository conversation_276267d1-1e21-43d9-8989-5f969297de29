<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DasWos - Login & Sign Up</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #4ecdc4;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            overflow-y: auto;
        }

        /* Animated background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(78, 205, 196, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(78, 205, 196, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(78, 205, 196, 0.05) 0%, transparent 50%);
            animation: backgroundPulse 8s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes backgroundPulse {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .container {
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #4ecdc4;
            border-radius: 20px;
            padding: 30px;
            max-width: 400px;
            width: 100%;
            max-height: 90vh;
            backdrop-filter: blur(20px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            position: relative;
            overflow-y: auto;
            margin: auto;
        }

        .container::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #4ecdc4, #45b7aa, #4ecdc4);
            border-radius: 20px;
            z-index: -1;
            animation: borderGlow 3s ease-in-out infinite;
        }

        @keyframes borderGlow {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .logo {
            text-align: center;
            margin-bottom: 20px;
        }

        .logo h1 {
            color: #fff;
            font-size: 2em;
            margin-bottom: 5px;
            text-shadow: 0 0 20px rgba(78, 205, 196, 0.5);
        }

        .logo p {
            color: #4ecdc4;
            font-size: 0.8em;
            opacity: 0.8;
        }

        .auth-tabs {
            display: flex;
            margin-bottom: 20px;
            border-radius: 10px;
            overflow: hidden;
            background: rgba(78, 205, 196, 0.1);
        }

        .tab-btn {
            flex: 1;
            padding: 12px;
            background: transparent;
            border: none;
            color: #4ecdc4;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            background: #4ecdc4;
            color: #000;
            font-weight: bold;
        }

        .tab-btn:hover:not(.active) {
            background: rgba(78, 205, 196, 0.2);
        }

        .form-container {
            position: relative;
        }

        .auth-form {
            display: none;
        }

        .auth-form.active {
            display: block;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #4ecdc4;
            font-size: 13px;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            background: rgba(78, 205, 196, 0.1);
            border: 2px solid rgba(78, 205, 196, 0.3);
            border-radius: 8px;
            color: #fff;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4ecdc4;
            background: rgba(78, 205, 196, 0.2);
            box-shadow: 0 0 15px rgba(78, 205, 196, 0.3);
        }

        .form-group input::placeholder {
            color: rgba(78, 205, 196, 0.6);
        }

        .submit-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(45deg, #4ecdc4, #45b7aa);
            border: none;
            border-radius: 8px;
            color: #000;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(78, 205, 196, 0.3);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        .error-message {
            color: #e74c3c;
            text-align: center;
            margin-bottom: 15px;
            font-size: 12px;
            min-height: 20px;
        }

        .success-message {
            color: #2ecc71;
            text-align: center;
            margin-bottom: 15px;
            font-size: 12px;
            min-height: 20px;
        }

        .back-link {
            text-align: center;
            margin-top: 15px;
        }

        .back-link a {
            color: #4ecdc4;
            text-decoration: none;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .back-link a:hover {
            color: #fff;
            text-shadow: 0 0 10px rgba(78, 205, 196, 0.5);
        }

        .demo-info {
            background: rgba(78, 205, 196, 0.1);
            border: 1px solid rgba(78, 205, 196, 0.3);
            border-radius: 8px;
            padding: 12px;
            margin-top: 15px;
            text-align: center;
            font-size: 11px;
        }

        .demo-info h4 {
            color: #fff;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .demo-info p {
            color: #4ecdc4;
            margin-bottom: 3px;
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }

            .container {
                padding: 20px;
                max-height: 95vh;
            }

            .logo h1 {
                font-size: 1.8em;
            }

            .form-group {
                margin-bottom: 12px;
            }

            .form-group input {
                padding: 10px;
                font-size: 12px;
            }

            .submit-btn {
                padding: 10px;
                font-size: 13px;
            }
        }

        @media (max-height: 700px) {
            .container {
                max-height: 95vh;
                padding: 20px;
            }

            .logo {
                margin-bottom: 15px;
            }

            .auth-tabs {
                margin-bottom: 15px;
            }

            .demo-info {
                margin-top: 10px;
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>🤖 DasWos</h1>
            <p>Your AI Shopping Assistant</p>
        </div>

        <div class="auth-tabs">
            <button class="tab-btn active" onclick="showTab('login')">Login</button>
            <button class="tab-btn" onclick="showTab('signup')">Sign Up</button>
        </div>

        <div class="form-container">
            <!-- Login Form -->
            <form id="loginForm" class="auth-form active">
                <div class="form-group">
                    <label for="loginUsername">Username</label>
                    <input type="text" id="loginUsername" name="username" placeholder="Enter your username" required>
                </div>
                <div class="form-group">
                    <label for="loginPassword">Password</label>
                    <input type="password" id="loginPassword" name="password" placeholder="Enter your password" required>
                </div>
                <div id="loginError" class="error-message"></div>
                <div id="loginSuccess" class="success-message"></div>
                <button type="submit" class="submit-btn">Login</button>
            </form>

            <!-- Sign Up Form -->
            <form id="signupForm" class="auth-form">
                <div class="form-group">
                    <label for="signupUsername">Username</label>
                    <input type="text" id="signupUsername" name="username" placeholder="Choose a username" required>
                </div>
                <div class="form-group">
                    <label for="signupEmail">Email</label>
                    <input type="email" id="signupEmail" name="email" placeholder="Enter your email" required>
                </div>
                <div class="form-group">
                    <label for="signupFullName">Full Name</label>
                    <input type="text" id="signupFullName" name="fullName" placeholder="Enter your full name" required>
                </div>
                <div class="form-group">
                    <label for="signupPassword">Password</label>
                    <input type="password" id="signupPassword" name="password" placeholder="Create a password" required>
                </div>
                <div class="form-group">
                    <label for="signupConfirmPassword">Confirm Password</label>
                    <input type="password" id="signupConfirmPassword" name="confirmPassword" placeholder="Confirm your password" required>
                </div>
                <div id="signupError" class="error-message"></div>
                <div id="signupSuccess" class="success-message"></div>
                <button type="submit" class="submit-btn">Sign Up</button>
            </form>
        </div>

        <div class="demo-info">
            <h4>Demo Account</h4>
            <p><strong>Username:</strong> admin</p>
            <p><strong>Password:</strong> admin123</p>
        </div>

        <div class="back-link">
            <a href="index.html">← Back to DasWos Robot</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        // Initialize Supabase client
        const SUPABASE_URL = 'https://azleuohipwepwkyoafhg.supabase.co';
        const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.GoMkaJQ8rYq1mBjEPfiH1OpF4mWEyZPtxXgby5XZ-jE';
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_KEY);

        // Tab switching
        function showTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Update forms
            document.querySelectorAll('.auth-form').forEach(form => form.classList.remove('active'));
            document.getElementById(tabName + 'Form').classList.add('active');

            // Clear messages
            clearMessages();
        }

        function clearMessages() {
            document.getElementById('loginError').textContent = '';
            document.getElementById('loginSuccess').textContent = '';
            document.getElementById('signupError').textContent = '';
            document.getElementById('signupSuccess').textContent = '';
        }

        // Password hashing function (same as main app)
        async function hashPassword(password) {
            const encoder = new TextEncoder();
            const data = encoder.encode(password + 'daswos_salt');
            const hashBuffer = await crypto.subtle.digest('SHA-256', data);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        }

        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;
            const errorDiv = document.getElementById('loginError');
            const successDiv = document.getElementById('loginSuccess');

            try {
                clearMessages();

                // Hash the password
                const hashedPassword = await hashPassword(password);

                // Check user credentials
                const { data: users, error } = await supabase
                    .from('users')
                    .select('*')
                    .eq('username', username)
                    .eq('password', hashedPassword)
                    .limit(1);

                if (error) throw error;

                if (users && users.length > 0) {
                    const user = users[0];

                    // Store user data
                    localStorage.setItem('daswos_user', JSON.stringify(user));

                    successDiv.textContent = 'Login successful! Redirecting...';

                    // Redirect to main app after 1 second
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1000);
                } else {
                    errorDiv.textContent = 'Invalid username or password';
                }

            } catch (error) {
                console.error('Login error:', error);
                errorDiv.textContent = error.message || 'Login failed. Please try again.';
            }
        });

        // Sign up form handler
        document.getElementById('signupForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const username = document.getElementById('signupUsername').value;
            const email = document.getElementById('signupEmail').value;
            const fullName = document.getElementById('signupFullName').value;
            const password = document.getElementById('signupPassword').value;
            const confirmPassword = document.getElementById('signupConfirmPassword').value;
            const errorDiv = document.getElementById('signupError');
            const successDiv = document.getElementById('signupSuccess');

            try {
                clearMessages();

                // Validate passwords match
                if (password !== confirmPassword) {
                    errorDiv.textContent = 'Passwords do not match';
                    return;
                }

                // Hash the password
                const hashedPassword = await hashPassword(password);

                // Create new user
                const { data: newUser, error } = await supabase
                    .from('users')
                    .insert({
                        username: username,
                        email: email,
                        full_name: fullName,
                        password: hashedPassword,
                        is_admin: false,
                        is_seller: false,
                        trust_score: 30
                    })
                    .select()
                    .single();

                if (error) {
                    if (error.code === '23505') { // Unique constraint violation
                        if (error.message.includes('username')) {
                            errorDiv.textContent = 'Username already exists';
                        } else if (error.message.includes('email')) {
                            errorDiv.textContent = 'Email already exists';
                        } else {
                            errorDiv.textContent = 'Username or email already exists';
                        }
                    } else {
                        throw error;
                    }
                    return;
                }

                successDiv.textContent = 'Account created successfully! You can now login.';

                // Clear form and switch to login tab
                document.getElementById('signupForm').reset();
                setTimeout(() => {
                    showTab('login');
                    document.getElementById('loginUsername').value = username;
                }, 2000);

            } catch (error) {
                console.error('Signup error:', error);
                errorDiv.textContent = error.message || 'Sign up failed. Please try again.';
            }
        });
    </script>
</body>
</html>
