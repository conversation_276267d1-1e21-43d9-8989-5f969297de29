<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DasWos - Login & Register</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            max-width: 400px;
            width: 100%;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            padding: 24px;
            text-align: center;
            border-bottom: 1px solid #e5e5e5;
        }

        .logo {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 16px;
        }

        .logo-text {
            font-size: 32px;
            font-weight: bold;
            color: black;
            margin-right: 8px;
        }

        .logo-box {
            width: 32px;
            height: 32px;
            background: black;
            position: relative;
        }

        .logo-box::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 14px;
            height: 10px;
            background: white;
        }

        .logo-box::after {
            content: '';
            position: absolute;
            bottom: 2px;
            left: 2px;
            width: 8px;
            height: 8px;
            background: white;
        }

        .tagline {
            color: #666;
            font-size: 14px;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            margin: 0;
        }

        .tab-btn {
            flex: 1;
            padding: 12px 16px;
            background: transparent;
            border: none;
            color: #666;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
        }

        .tab-btn.active {
            background: white;
            color: black;
            border-bottom: 2px solid black;
            font-weight: 500;
        }

        .tab-btn:hover:not(.active) {
            background: #f0f0f0;
        }

        .form-container {
            padding: 24px;
        }

        .auth-form {
            display: none;
        }

        .auth-form.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-size: 14px;
            font-weight: 500;
        }

        .input-container {
            position: relative;
        }

        .form-group input {
            width: 100%;
            padding: 12px 40px 12px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            color: #333;
            font-size: 14px;
            transition: all 0.2s ease;
            background: white;
        }

        .form-group input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .form-group input::placeholder {
            color: #999;
        }

        .input-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            width: 16px;
            height: 16px;
        }

        .submit-btn {
            width: 100%;
            padding: 12px;
            background: black;
            border: none;
            border-radius: 6px;
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 15px;
        }

        .submit-btn:hover {
            background: #333;
        }

        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .error-message {
            color: #dc3545;
            text-align: center;
            margin-bottom: 15px;
            font-size: 14px;
            min-height: 20px;
        }

        .success-message {
            color: #28a745;
            text-align: center;
            margin-bottom: 15px;
            font-size: 14px;
            min-height: 20px;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
            padding: 16px;
            border-top: 1px solid #e5e5e5;
        }

        .back-link a {
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
        }

        .back-link a:hover {
            text-decoration: underline;
        }

        .demo-info {
            background: #f8f9fa;
            border: 1px solid #e5e5e5;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 12px;
        }

        .demo-info h4 {
            color: #333;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .demo-info p {
            color: #666;
            margin-bottom: 3px;
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }

            .container {
                max-width: 100%;
            }

            .header {
                padding: 16px;
            }

            .form-container {
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <span class="logo-text">daswos</span>
                <div class="logo-box"></div>
            </div>
            <p class="tagline">The trusted search platform with verified sellers</p>
        </div>

        <div class="tabs">
            <button class="tab-btn active" onclick="showTab('login')">Login</button>
            <button class="tab-btn" onclick="showTab('register')">Register</button>
        </div>

        <div class="form-container">
            <!-- Demo Account Info -->
            <div class="demo-info">
                <h4>Demo Account</h4>
                <p><strong>Username:</strong> admin</p>
                <p><strong>Password:</strong> admin123</p>
            </div>

            <!-- Login Form -->
            <form id="loginForm" class="auth-form active">
                <div class="form-group">
                    <label for="loginUsername">Username</label>
                    <div class="input-container">
                        <input type="text" id="loginUsername" name="username" placeholder="Your username or email" required>
                        <svg class="input-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"/>
                            <circle cx="12" cy="10" r="3"/>
                            <path d="M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662"/>
                        </svg>
                    </div>
                    <p style="font-size: 12px; color: #666; margin-top: 4px;">You can use either your username or email to log in (case-insensitive)</p>
                </div>
                <div class="form-group">
                    <label for="loginPassword">Password</label>
                    <div class="input-container">
                        <input type="password" id="loginPassword" name="password" placeholder="Your password" required>
                        <svg class="input-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect width="18" height="11" x="3" y="11" rx="2" ry="2"/>
                            <path d="m7 11V7a5 5 0 0 1 10 0v4"/>
                        </svg>
                    </div>
                </div>
                <div id="loginError" class="error-message"></div>
                <div id="loginSuccess" class="success-message"></div>
                <button type="submit" class="submit-btn">Login</button>
            </form>

            <!-- Register Form -->
            <form id="registerForm" class="auth-form">
                <div class="form-group">
                    <label for="registerUsername">Username</label>
                    <input type="text" id="registerUsername" name="username" placeholder="Choose a username" required>
                </div>
                <div class="form-group">
                    <label for="registerEmail">Email</label>
                    <input type="email" id="registerEmail" name="email" placeholder="Enter your email" required>
                </div>
                <div class="form-group">
                    <label for="registerFullName">Full Name</label>
                    <input type="text" id="registerFullName" name="fullName" placeholder="Enter your full name" required>
                </div>
                <div class="form-group">
                    <label for="registerPassword">Password</label>
                    <input type="password" id="registerPassword" name="password" placeholder="Create a password" required>
                </div>
                <div class="form-group">
                    <label for="registerConfirmPassword">Confirm Password</label>
                    <input type="password" id="registerConfirmPassword" name="confirmPassword" placeholder="Confirm your password" required>
                </div>
                <div id="registerError" class="error-message"></div>
                <div id="registerSuccess" class="success-message"></div>
                <button type="submit" class="submit-btn">Register</button>
            </form>
        </div>

        <div class="back-link">
            <a href="index.html">← Back to DasWos Robot</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="https://cdn.jsdelivr.net/npm/scrypt-js@3.0.1/scrypt.js"></script>
    <script>
        // Initialize Supabase client
        const SUPABASE_URL = 'https://tqetghhuxpqogxzbwqwe.supabase.co';
        const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRxZXRnaGh1eHBxb2d4emJ3cXdlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYxOTE0NywiZXhwIjoyMDY0MTk1MTQ3fQ.XlT3knuZpj4Q5FMwNU9iGgRhh7krifp90OwfCrJ4PCo';
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_KEY);

        // Tab switching
        function showTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Update forms
            document.querySelectorAll('.auth-form').forEach(form => form.classList.remove('active'));
            document.getElementById(tabName + 'Form').classList.add('active');

            // Clear messages
            clearMessages();
        }

        function clearMessages() {
            document.getElementById('loginError').textContent = '';
            document.getElementById('loginSuccess').textContent = '';
            document.getElementById('registerError').textContent = '';
            document.getElementById('registerSuccess').textContent = '';
        }

        // Scrypt password hashing (same as daswos-18)
        async function hashPassword(password) {
            try {
                // Generate random salt (16 bytes)
                const salt = new Uint8Array(16);
                crypto.getRandomValues(salt);

                // Convert password to bytes
                const passwordBytes = new TextEncoder().encode(password);

                // Use scrypt to hash the password
                const hashedBytes = await scrypt(passwordBytes, salt, 16384, 8, 1, 64);

                // Convert to hex and combine with salt
                const hashedHex = Array.from(hashedBytes).map(b => b.toString(16).padStart(2, '0')).join('');
                const saltHex = Array.from(salt).map(b => b.toString(16).padStart(2, '0')).join('');

                return `${hashedHex}.${saltHex}`;
            } catch (error) {
                console.error('Error hashing password:', error);
                throw new Error('Password hashing failed');
            }
        }

        // Function to verify password against stored hash (matches daswos-18 exactly)
        async function verifyPassword(password, storedHash) {
            try {
                if (!storedHash || !storedHash.includes('.')) {
                    console.error('Invalid stored password format');
                    return false;
                }

                const [hashedHex, saltHex] = storedHash.split('.');

                if (!hashedHex || !saltHex) {
                    console.error('Invalid stored password format, missing hash or salt');
                    return false;
                }

                // Use the same scrypt parameters as daswos-18: N=16384, r=8, p=1, dkLen=64
                const passwordBytes = new TextEncoder().encode(password);
                const saltBytes = new Uint8Array(saltHex.match(/.{2}/g).map(byte => parseInt(byte, 16)));

                // Hash the supplied password with the stored salt using same parameters as Node.js
                const suppliedHashBytes = await scrypt(passwordBytes, saltBytes, 16384, 8, 1, 64);

                // Convert stored hash from hex to bytes for comparison
                const storedHashBytes = new Uint8Array(hashedHex.match(/.{2}/g).map(byte => parseInt(byte, 16)));

                // Compare the hashes using timing-safe comparison
                if (storedHashBytes.length !== suppliedHashBytes.length) {
                    return false;
                }

                let result = 0;
                for (let i = 0; i < storedHashBytes.length; i++) {
                    result |= storedHashBytes[i] ^ suppliedHashBytes[i];
                }

                return result === 0;
            } catch (error) {
                console.error('Error verifying password:', error);
                return false;
            }
        }

        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;
            const errorDiv = document.getElementById('loginError');
            const successDiv = document.getElementById('loginSuccess');

            try {
                clearMessages();

                // Get user by username first
                const { data: users, error } = await supabase
                    .from('users')
                    .select('*')
                    .eq('username', username)
                    .limit(1);

                if (error) throw error;

                if (users && users.length > 0) {
                    const user = users[0];

                    // Verify password using scrypt
                    const passwordValid = await verifyPassword(password, user.password);

                    if (passwordValid) {
                        // Store user data
                        localStorage.setItem('daswos_user', JSON.stringify(user));

                        successDiv.textContent = `Welcome, ${user.username}! Redirecting...`;

                        // Redirect to main app after 1.5 seconds to show welcome message
                        setTimeout(() => {
                            window.location.href = 'index.html';
                        }, 1500);
                    } else {
                        errorDiv.textContent = 'Invalid username or password';
                    }
                } else {
                    errorDiv.textContent = 'Invalid username or password';
                }

            } catch (error) {
                console.error('Login error:', error);
                errorDiv.textContent = error.message || 'Login failed. Please try again.';
            }
        });

        // Sign up form handler
        document.getElementById('signupForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const username = document.getElementById('signupUsername').value;
            const email = document.getElementById('signupEmail').value;
            const fullName = document.getElementById('signupFullName').value;
            const password = document.getElementById('signupPassword').value;
            const confirmPassword = document.getElementById('signupConfirmPassword').value;
            const errorDiv = document.getElementById('signupError');
            const successDiv = document.getElementById('signupSuccess');

            try {
                clearMessages();

                // Validate passwords match
                if (password !== confirmPassword) {
                    errorDiv.textContent = 'Passwords do not match';
                    return;
                }

                // Hash the password
                const hashedPassword = await hashPassword(password);

                // Create new user
                const { data: newUser, error } = await supabase
                    .from('users')
                    .insert({
                        username: username,
                        email: email,
                        full_name: fullName,
                        password: hashedPassword,
                        is_admin: false,
                        is_seller: false,
                        trust_score: 30
                    })
                    .select()
                    .single();

                if (error) {
                    if (error.code === '23505') { // Unique constraint violation
                        if (error.message.includes('username')) {
                            errorDiv.textContent = 'Username already exists';
                        } else if (error.message.includes('email')) {
                            errorDiv.textContent = 'Email already exists';
                        } else {
                            errorDiv.textContent = 'Username or email already exists';
                        }
                    } else {
                        throw error;
                    }
                    return;
                }

                successDiv.textContent = `Welcome to DasWos, ${username}! Account created successfully. You can now login.`;

                // Clear form and switch to login tab
                document.getElementById('signupForm').reset();
                setTimeout(() => {
                    showTab('login');
                    document.getElementById('loginUsername').value = username;
                }, 2500);

            } catch (error) {
                console.error('Signup error:', error);
                errorDiv.textContent = error.message || 'Sign up failed. Please try again.';
            }
        });
    </script>
</body>
</html>
