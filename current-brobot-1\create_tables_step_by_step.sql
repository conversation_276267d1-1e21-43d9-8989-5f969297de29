-- STEP-BY-<PERSON>EP TABLE CREATION FOR UNIFIED SCHEMA
-- Execute each section separately in Supabase SQL Editor

-- STEP 1: Enable Extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- STEP 2: Create Users Table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    email TEXT NOT NULL UNIQUE,
    full_name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_seller BOOLEAN DEFAULT false NOT NULL,
    is_admin BOOLEAN DEFAULT false NOT NULL,
    avatar TEXT,
    has_subscription BOOLEAN DEFAULT false NOT NULL,
    subscription_type TEXT,
    subscription_expires_at TIMESTAMP WITH TIME ZONE,
    is_family_owner BOOLEAN DEFAULT false NOT NULL,
    family_owner_id INTEGER,
    parent_account_id INTEGER,
    is_child_account BOOLEAN DEFAULT false NOT NULL,
    super_safe_mode BO<PERSON>EAN DEFAULT false NOT NULL,
    super_safe_settings JSONB DEFAULT '{"blockGambling": true, "blockAdultContent": true, "blockOpenSphere": false}',
    safe_sphere_active BOOLEAN DEFAULT false NOT NULL,
    ai_shopper_enabled BOOLEAN DEFAULT false NOT NULL,
    ai_shopper_settings JSONB DEFAULT '{}',
    identity_verified BOOLEAN DEFAULT false NOT NULL,
    identity_verification_status TEXT DEFAULT 'none' NOT NULL,
    identity_verification_submitted_at TIMESTAMP WITH TIME ZONE,
    identity_verification_approved_at TIMESTAMP WITH TIME ZONE,
    identity_verification_data JSONB DEFAULT '{}',
    trust_score INTEGER DEFAULT 30 NOT NULL,
    daswos_coins_balance INTEGER DEFAULT 0 NOT NULL,
    business_name TEXT,
    business_type TEXT DEFAULT 'individual' NOT NULL,
    business_address TEXT,
    contact_phone TEXT,
    tax_id TEXT,
    website TEXT,
    year_established INTEGER,
    business_description TEXT,
    profile_image_url TEXT,
    document_urls TEXT[],
    updated_at TIMESTAMP WITH TIME ZONE
);

-- STEP 3: Create Categories Table
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    slug TEXT UNIQUE,
    description TEXT,
    parent_id INTEGER,
    level INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- STEP 4: Create Products Table
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    price INTEGER NOT NULL,
    image_url TEXT NOT NULL,
    seller_id INTEGER NOT NULL,
    seller_name TEXT NOT NULL,
    seller_verified BOOLEAN NOT NULL DEFAULT false,
    seller_type TEXT NOT NULL DEFAULT 'merchant',
    trust_score INTEGER NOT NULL DEFAULT 30,
    identity_verified BOOLEAN NOT NULL DEFAULT false,
    identity_verification_status TEXT NOT NULL DEFAULT 'none',
    tags TEXT[] NOT NULL DEFAULT '{}',
    shipping TEXT NOT NULL DEFAULT 'standard',
    original_price INTEGER,
    discount INTEGER,
    verified_since TEXT,
    warning TEXT,
    is_bulk_buy BOOLEAN DEFAULT false NOT NULL,
    bulk_minimum_quantity INTEGER,
    bulk_discount_rate INTEGER,
    image_description TEXT,
    category_id INTEGER,
    ai_attributes JSONB DEFAULT '{}',
    search_vector TEXT,
    status TEXT NOT NULL DEFAULT 'active',
    quantity INTEGER NOT NULL DEFAULT 1,
    sold_quantity INTEGER NOT NULL DEFAULT 0,
    in_stock BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- STEP 5: Create User Sessions Table
CREATE TABLE user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    session_token TEXT NOT NULL UNIQUE,
    device_info JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true NOT NULL,
    last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- STEP 6: Create Cart Items Table
CREATE TABLE cart_items (
    id SERIAL PRIMARY KEY,
    user_id INTEGER,
    session_id INTEGER,
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE,
    source TEXT NOT NULL DEFAULT 'manual',
    recommendation_id INTEGER
);

-- STEP 7: Create Information Content Table
CREATE TABLE information_content (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    summary TEXT NOT NULL,
    source_url TEXT NOT NULL,
    source_name TEXT NOT NULL,
    source_verified BOOLEAN NOT NULL DEFAULT false,
    source_type TEXT NOT NULL DEFAULT 'website',
    trust_score INTEGER NOT NULL,
    category TEXT NOT NULL,
    tags TEXT[] NOT NULL,
    image_url TEXT,
    verified_since TEXT,
    warning TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- STEP 8: Create Purchases Table
CREATE TABLE purchases (
    id SERIAL PRIMARY KEY,
    buyer_id INTEGER NOT NULL,
    seller_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    total_price INTEGER NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    transaction_id INTEGER,
    purchased_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    received_at TIMESTAMP WITH TIME ZONE,
    rating INTEGER,
    review_comment TEXT,
    rated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- STEP 9: Create Seller Verification Table
CREATE TABLE seller_verification (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    type TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    processed_at TIMESTAMP WITH TIME ZONE,
    deposit_amount INTEGER,
    comments TEXT,
    document_urls TEXT[]
);

-- STEP 10: Create DasWos Coins Transactions Table
CREATE TABLE daswos_coins_transactions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    amount INTEGER NOT NULL,
    type TEXT NOT NULL,
    description TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'completed',
    metadata JSONB DEFAULT '{}',
    related_order_id INTEGER,
    related_split_buy_id INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- STEP 11: Create User Product Content Table
CREATE TABLE user_product_content (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    content_type TEXT NOT NULL,
    content TEXT NOT NULL,
    rating INTEGER,
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE
);
