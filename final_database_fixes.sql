-- Final database fixes for unified authentication
-- Run this in Supabase SQL Editor

-- 1. Fix app_settings table (required for daswos-18)
DROP TABLE IF EXISTS app_settings CASCADE;

CREATE TABLE app_settings (
    id SERIAL PRIMARY KEY,
    key TEXT NOT NULL UNIQUE,
    value JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Add trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger for app_settings
CREATE TRIGGER update_app_settings_updated_at 
    BEFORE UPDATE ON app_settings 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 2. Verify the henry user exists and check password format
SELECT 
    username,
    email,
    CASE 
        WHEN password LIKE '%.%' THEN 'Scrypt format (compatible)'
        WHEN LENGTH(password) = 64 THEN 'SHA-256 format (needs migration)'
        ELSE 'Unknown format'
    END as password_format,
    LENGTH(password) as password_length,
    created_at
FROM users 
WHERE username = 'henry';

-- 3. Check all users and their password formats
SELECT 
    username,
    email,
    CASE 
        WHEN password LIKE '%.%' THEN 'Scrypt format'
        WHEN LENGTH(password) = 64 THEN 'SHA-256 format'
        ELSE 'Unknown format'
    END as password_format,
    is_admin,
    is_seller,
    created_at
FROM users 
ORDER BY created_at DESC;

-- 4. Success message
SELECT 
    'DATABASE FIXES APPLIED SUCCESSFULLY!' as status,
    'app_settings table created with correct schema' as fix_1,
    'Password formats verified' as fix_2,
    'Ready for unified authentication testing' as next_step;

-- 5. Test instructions
SELECT 
    'TESTING INSTRUCTIONS:' as step,
    '1. Open test_password_verification.html in browser' as test_1,
    '2. Click "Test with Supabase Database"' as test_2,
    '3. Should show successful password verification for henry/Testtest1' as test_3,
    '4. Then test login in current-brobot-1 with henry/Testtest1' as test_4;
