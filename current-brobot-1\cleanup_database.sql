-- CLEANUP SCRIPT - Use this to completely reset the database
-- WARNING: This will delete ALL data and tables!

-- Drop all views first
DROP VIEW IF EXISTS products_legacy CASCADE;
DROP VIEW IF EXISTS categories_legacy CASCADE;

-- Drop all tables in correct order (respecting foreign key dependencies)
DROP TABLE IF EXISTS user_product_content CASCADE;
DROP TABLE IF EXISTS daswos_coins_transactions CASCADE;
DROP TABLE IF EXISTS seller_verification CASCADE;
DROP TABLE IF EXISTS purchases CASCADE;
DROP TABLE IF EXISTS information_content CASCADE;
DROP TABLE IF EXISTS cart_items CASCADE;
DROP TABLE IF EXISTS user_sessions CASCADE;
DROP TABLE IF EXISTS products CASCADE;
DROP TABLE IF EXISTS categories CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Drop functions
DROP FUNCTION IF EXISTS search_products(TEXT);
DROP FUNCTION IF EXISTS update_updated_at_column();

-- Confirm cleanup
SELECT 'Database cleaned - all tables dropped' as status;
