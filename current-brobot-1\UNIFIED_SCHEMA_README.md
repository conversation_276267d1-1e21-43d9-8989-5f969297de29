# 🗄️ Unified Database Schema for DasWos Apps

This document describes the unified database schema that allows both **DasWos-18** and **Current-Brobot-1** apps to share the same database while maintaining compatibility.

## 🎯 **Overview**

The unified schema combines the best features from both apps and includes all the important tables you requested:

- ✅ **cart_items** - Shopping cart functionality
- ✅ **categories** - Product categorization
- ✅ **products** - Product catalog with enhanced seller info
- ✅ **information_content** - Information/content management
- ✅ **user_sessions** - Session management for both apps
- ✅ **users** - Enhanced user system with all features
- ✅ **user_product_content** - User-generated content
- ✅ **purchases** - Purchase history and transactions
- ✅ **seller_verification** - Seller verification system
- ✅ **daswos_coins_transactions** - Virtual currency system

## 🚀 **Quick Setup**

### 1. Apply the Unified Schema

```sql
-- Execute the unified_schema.sql file in your Supabase SQL editor
-- This will create all tables, constraints, indexes, and sample data
```

### 2. Use the Setup Tool

Visit: `http://localhost:8080/setup-unified-database.html`

1. **Check Current Database** - See what exists
2. **Apply Unified Schema** - Verify schema compatibility  
3. **Migrate Existing Data** - Check data compatibility
4. **Test Unified Database** - Verify both apps work

### 3. Test Login Functionality

**Current-Brobot-1** now includes login functionality:
- Username: `admin`
- Password: `admin123`

## 📊 **Schema Highlights**

### **Enhanced Users Table**
```sql
- Basic auth: username, password, email
- Seller features: is_seller, trust_score, business_info
- Family accounts: family_owner_id, parent_account_id
- SuperSafe mode: super_safe_mode, safe_sphere_active
- AI features: ai_shopper_enabled, ai_shopper_settings
- Identity verification: identity_verified, verification_status
- DasWos coins: daswos_coins_balance
```

### **Enhanced Products Table**
```sql
- Basic info: title, description, price (in cents)
- Seller info: seller_id, seller_name, trust_score
- Categories: category_id with foreign key
- Advanced features: bulk_buy, ai_attributes
- Compatibility: in_stock field for current-brobot-1
```

### **Session Management**
```sql
- Cross-app sessions: user_sessions table
- Device tracking: device_info JSON
- Expiration handling: expires_at timestamps
- Security: session tokens and active status
```

## 🔄 **App Compatibility**

### **DasWos-18 Compatibility**
- All existing features preserved
- Enhanced with new unified fields
- Backward compatible queries via views
- Session management integrated

### **Current-Brobot-1 Compatibility**
- Added login functionality similar to DasWos-18
- Product search works with enhanced schema
- Background product system unchanged
- Price conversion handled (cents ↔ dollars)

## 🛠️ **New Features Added to Current-Brobot-1**

### **Login System**
- **Login Modal**: Clean, themed login interface
- **Session Management**: Persistent login across browser sessions
- **User Display**: Shows logged-in user in top-right corner
- **Logout Functionality**: Secure session termination

### **Database Integration**
- **Unified Queries**: Works with enhanced product schema
- **User Authentication**: SHA-256 password hashing
- **Session Tokens**: Secure session management
- **Backward Compatibility**: Legacy views for old queries

## 📁 **File Structure**

```
current-brobot-1/
├── unified_schema.sql              # Complete unified schema
├── setup-unified-database.html     # Setup and testing tool
├── index.html                      # Enhanced with login
├── UNIFIED_SCHEMA_README.md        # This file
└── supabase_schema.sql            # Original schema (legacy)
```

## 🔧 **Implementation Details**

### **Password Hashing**
```javascript
// Simple SHA-256 hashing for demo
// In production, use proper bcrypt
async function hashPassword(password) {
    const encoder = new TextEncoder();
    const data = encoder.encode(password + 'daswos_salt');
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}
```

### **Session Management**
```javascript
// Create session with 7-day expiration
const sessionToken = generateSessionToken();
const expiresAt = new Date();
expiresAt.setDate(expiresAt.getDate() + 7);
```

### **Price Compatibility**
```sql
-- View for current-brobot-1 compatibility
CREATE VIEW products_legacy AS
SELECT 
    id, title, description,
    CAST(price AS DECIMAL(10,2)) / 100 AS price, -- Convert cents to dollars
    image_url, category_id, tags, in_stock, created_at
FROM products WHERE status = 'active';
```

## 🎮 **Usage Examples**

### **Login to Current-Brobot-1**
1. Click "Login" button
2. Enter credentials (admin/admin123)
3. User info appears in top-right
4. Session persists across browser refreshes

### **Search Products**
1. Type search term in search bar
2. Results appear in popup (not floating)
3. Click results for detailed view
4. Background products continue floating independently

### **Background Product System**
1. Random products float automatically
2. Products shuffle every 20 seconds (2 at a time)
3. No duplicates displayed simultaneously
4. Click floating products for details

## 🔒 **Security Features**

- **Password Hashing**: SHA-256 with salt
- **Session Tokens**: Unique, time-limited tokens
- **Session Validation**: Server-side session verification
- **Automatic Cleanup**: Expired sessions handled
- **CSRF Protection**: Session-based authentication

## 🚀 **Next Steps**

1. **Apply Schema**: Execute `unified_schema.sql` in Supabase
2. **Test Both Apps**: Verify DasWos-18 and Current-Brobot-1 work
3. **Add Users**: Create additional test users
4. **Customize**: Modify schema for specific needs
5. **Deploy**: Use unified schema in production

## 📞 **Support**

If you encounter issues:
1. Check the setup tool at `/setup-unified-database.html`
2. Verify Supabase connection
3. Check browser console for errors
4. Ensure all required tables exist

---

**🎉 Both apps now share a unified, feature-rich database schema!**
