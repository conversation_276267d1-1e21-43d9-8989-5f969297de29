
import { createClient } from '@supabase/supabase-js'

// Main app database (daswos-app project)
const supabaseUrl = 'https://tqetghhuxpqogxzbwqwe.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRxZXRnaGh1eHBxb2d4emJ3cXdlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYxOTE0NywiZXhwIjoyMDY0MTk1MTQ3fQ.XlT3knuZpj4Q5FMwNU9iGgRhh7krifp90OwfCrJ4PCo'

export const supabase = createClient(supabaseUrl, supabaseKey)

// Function to search products
export async function searchProducts(query) {
  try {
    console.log('Searching for:', query);

    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories (name)
      `)
      .or(`title.ilike.%${query}%,description.ilike.%${query}%,tags.cs.{"${query}"}`)
      .order('title', { ascending: true })
      .limit(20);

    if (error) {
      console.error('Error searching products:', error);
      throw error;
    }

    console.log('Search results:', data);
    return data || [];
  } catch (error) {
    console.error('Error in searchProducts:', error);
    throw error; // Re-throw the error to handle it in the calling function
  }
}

// Function to get random products
export async function getRandomProducts(limit = 5) {
  try {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('status', 'active')
      .limit(limit)

    if (error) {
      console.error('Error getting random products:', error)
      return []
    }

    // Shuffle the results
    return (data || []).sort(() => Math.random() - 0.5)
  } catch (error) {
    console.error('Error in getRandomProducts:', error)
    return []
  }
}
