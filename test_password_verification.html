<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Password Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Password Verification Test</h1>
        
        <div class="info">
            <strong>Purpose:</strong> Test if current-brobot-1 can verify passwords created by daswos-18.<br>
            <strong>Test User:</strong> henry / Testtest1 (created in daswos-18)
        </div>

        <button onclick="testPasswordVerification()">Test Password Verification</button>
        <button onclick="testWithSupabase()">Test with Supabase Database</button>
        
        <div id="results"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="https://cdn.jsdelivr.net/npm/scrypt-js@3.0.1/scrypt.js"></script>
    <script>
        // Initialize Supabase client
        const SUPABASE_URL = 'https://azleuohipwepwkyoafhg.supabase.co';
        const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF6bGV1b2hpcHdlcHdreW9hZmhnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYxMjIxNywiZXhwIjoyMDY0MTg4MjE3fQ.GoMkaJQ8rYq1mBjEPfiH1OpF4mWEyZPtxXgby5XZ-jE';
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_KEY);

        // Function to verify password against stored hash (matches daswos-18 exactly)
        async function verifyPassword(password, storedHash) {
            try {
                if (!storedHash || !storedHash.includes('.')) {
                    console.error('Invalid stored password format');
                    return false;
                }

                const [hashedHex, saltHex] = storedHash.split('.');
                
                if (!hashedHex || !saltHex) {
                    console.error('Invalid stored password format, missing hash or salt');
                    return false;
                }

                // Use the same scrypt parameters as daswos-18: N=16384, r=8, p=1, dkLen=64
                const passwordBytes = new TextEncoder().encode(password);
                const saltBytes = new Uint8Array(saltHex.match(/.{2}/g).map(byte => parseInt(byte, 16)));
                
                // Hash the supplied password with the stored salt using same parameters as Node.js
                const suppliedHashBytes = await scrypt(passwordBytes, saltBytes, 16384, 8, 1, 64);
                
                // Convert stored hash from hex to bytes for comparison
                const storedHashBytes = new Uint8Array(hashedHex.match(/.{2}/g).map(byte => parseInt(byte, 16)));
                
                // Compare the hashes using timing-safe comparison
                if (storedHashBytes.length !== suppliedHashBytes.length) {
                    return false;
                }
                
                let result = 0;
                for (let i = 0; i < storedHashBytes.length; i++) {
                    result |= storedHashBytes[i] ^ suppliedHashBytes[i];
                }
                
                return result === 0;
            } catch (error) {
                console.error('Error verifying password:', error);
                return false;
            }
        }

        function addResult(message, isSuccess) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        async function testPasswordVerification() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h3>Testing Password Verification...</h3>';

            // Test with a known scrypt hash (this would be from daswos-18)
            const testPassword = 'Testtest1';
            const testHash = 'example.hash'; // This would be the actual hash from database
            
            addResult('🔍 Testing password verification function...', true);
            addResult('⚠️ Need actual hash from database to test properly', false);
        }

        async function testWithSupabase() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h3>Testing with Supabase Database...</h3>';

            try {
                addResult('🔍 Connecting to Supabase...', true);

                // Get the henry user from database
                const { data: users, error } = await supabase
                    .from('users')
                    .select('*')
                    .eq('username', 'henry')
                    .limit(1);

                if (error) {
                    addResult(`❌ Database error: ${error.message}`, false);
                    return;
                }

                if (!users || users.length === 0) {
                    addResult('❌ User "henry" not found in database', false);
                    return;
                }

                const user = users[0];
                addResult(`✅ Found user: ${user.username} (${user.email})`, true);
                addResult(`🔐 Password hash format: ${user.password.substring(0, 20)}...`, true);

                // Test password verification
                const testPassword = 'Testtest1';
                addResult(`🧪 Testing password: "${testPassword}"`, true);

                const isValid = await verifyPassword(testPassword, user.password);
                
                if (isValid) {
                    addResult('✅ PASSWORD VERIFICATION SUCCESSFUL! 🎉', true);
                    addResult('✅ current-brobot-1 can now authenticate users created in daswos-18', true);
                } else {
                    addResult('❌ Password verification failed', false);
                    addResult('❌ There may still be a compatibility issue', false);
                }

            } catch (error) {
                addResult(`❌ Test failed: ${error.message}`, false);
                console.error('Test error:', error);
            }
        }
    </script>
</body>
</html>
