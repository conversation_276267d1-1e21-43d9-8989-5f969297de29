-- SIMPLE TABLE CREATION - Execute each section separately

-- SECTION 1: Extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- SECTION 2: Users table (most important)
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    email TEXT NOT NULL UNIQUE,
    full_name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_seller BOOLEAN DEFAULT false NOT NULL,
    is_admin BOOLEAN DEFAULT false NOT NULL,
    trust_score INTEGER DEFAULT 30 NOT NULL
);

-- SECTION 3: Categories table
CREATE TABLE IF NOT EXISTS categories (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    slug TEXT UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SECTION 4: Products table
CREATE TABLE IF NOT EXISTS products (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    price INTEGER NOT NULL,
    image_url TEXT NOT NULL,
    seller_id INTEGER NOT NULL,
    seller_name TEXT NOT NULL,
    category_id INTEGER,
    tags TEXT[] NOT NULL DEFAULT '{}',
    trust_score INTEGER NOT NULL DEFAULT 30,
    status TEXT NOT NULL DEFAULT 'active',
    quantity INTEGER NOT NULL DEFAULT 1,
    in_stock BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SECTION 5: User Sessions table
CREATE TABLE IF NOT EXISTS user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    session_token TEXT NOT NULL UNIQUE,
    device_info JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- SECTION 6: Cart Items table
CREATE TABLE IF NOT EXISTS cart_items (
    id SERIAL PRIMARY KEY,
    user_id INTEGER,
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- SECTION 7: Add basic foreign keys
ALTER TABLE products 
    ADD CONSTRAINT fk_products_seller 
    FOREIGN KEY (seller_id) 
    REFERENCES users(id) 
    ON DELETE CASCADE;

ALTER TABLE products 
    ADD CONSTRAINT fk_products_category 
    FOREIGN KEY (category_id) 
    REFERENCES categories(id) 
    ON DELETE SET NULL;

ALTER TABLE user_sessions 
    ADD CONSTRAINT fk_user_sessions_user 
    FOREIGN KEY (user_id) 
    REFERENCES users(id) 
    ON DELETE CASCADE;

ALTER TABLE cart_items 
    ADD CONSTRAINT fk_cart_items_user 
    FOREIGN KEY (user_id) 
    REFERENCES users(id) 
    ON DELETE CASCADE;

ALTER TABLE cart_items 
    ADD CONSTRAINT fk_cart_items_product 
    FOREIGN KEY (product_id) 
    REFERENCES products(id) 
    ON DELETE CASCADE;

-- SECTION 8: Insert sample data
INSERT INTO categories (name, slug, description) VALUES
    ('Electronics', 'electronics', 'Electronic devices and accessories'),
    ('Clothing', 'clothing', 'Apparel and fashion items'),
    ('Home & Garden', 'home-garden', 'Home improvement and gardening supplies'),
    ('Sports', 'sports', 'Sports equipment and gear'),
    ('Toys', 'toys', 'Toys and games')
ON CONFLICT (slug) DO NOTHING;

INSERT INTO users (username, password, email, full_name, is_admin, is_seller, trust_score) VALUES 
    ('admin', '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9', '<EMAIL>', 'Admin User', true, true, 100)
ON CONFLICT (username) DO NOTHING;

INSERT INTO products (title, description, price, image_url, seller_id, seller_name, category_id, tags, trust_score, status, quantity) VALUES
    ('Wireless Headphones', 'High-quality wireless headphones with noise cancellation', 19999, 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop', 1, 'admin', 1, '{"electronics", "audio", "wireless"}', 85, 'active', 10),
    ('Running Shoes', 'Lightweight running shoes for all terrains', 8999, 'https://images.unsplash.com/photo-**********-7eec264c27ff?w=400&h=400&fit=crop', 1, 'admin', 2, '{"footwear", "sports", "running"}', 85, 'active', 15),
    ('Smart Watch', 'Feature-rich smartwatch with health tracking', 24999, 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop', 1, 'admin', 1, '{"electronics", "wearables", "fitness"}', 85, 'active', 8),
    ('Yoga Mat', 'Non-slip yoga mat for all skill levels', 2999, 'https://images.unsplash.com/photo-1576678927484-cc907957088c?w=400&h=400&fit=crop', 1, 'admin', 4, '{"fitness", "yoga", "home"}', 85, 'active', 20),
    ('Blender', 'High-powered blender for smoothies and food prep', 12999, 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400&h=400&fit=crop', 1, 'admin', 3, '{"kitchen", "appliances"}', 85, 'active', 5);

-- SECTION 9: Verify creation
SELECT 'Tables created successfully!' as status;
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE' ORDER BY table_name;
