
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Daswos Robot Animation</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <!-- Load Supabase first with onload handler -->
    <script>
    // Global Supabase client instance - will be initialized after the library loads

    // Function to initialize the application after Supabase is loaded
    function initApp() {
        console.log('All scripts loaded, initializing app...');
        document.removeEventListener('DOMContentLoaded', initApp);

        // Initialize the app
        initializeApp().catch(error => {
            console.error('Failed to initialize app:', error);
            showError(`Initialization failed: ${error.message}`);
        });
    }

    // Load scripts in order
    function loadScripts() {
        console.log('Loading Supabase...');
        const supabaseScript = document.createElement('script');
        supabaseScript.src = 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2';
        supabaseScript.onload = () => {
            console.log('Supabase loaded, now loading scrypt...');
            const scryptScript = document.createElement('script');
            scryptScript.src = 'https://cdn.jsdelivr.net/npm/scrypt-js@3.0.1/scrypt.js';
            scryptScript.onload = () => {
                console.log('Scrypt loaded, now loading p5.js...');
                const p5Script = document.createElement('script');
                p5Script.src = 'https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js';
                p5Script.onload = () => {
                    console.log('All scripts loaded, initializing application...');
                    if (document.readyState === 'loading') {
                        document.addEventListener('DOMContentLoaded', initApp);
                    } else {
                        initApp();
                    }
                };
                document.head.appendChild(p5Script);
            };
            document.head.appendChild(scryptScript);
        };
        document.head.appendChild(supabaseScript);
    }

    // Start loading scripts
    loadScripts();
    </script>

    <!-- Our application code -->
    <script>

    // Global Supabase client - will be set after initialization
    let supabaseClient = null;

    // Function to show error message
    function showError(message) {
        console.error(message);
        const statusElement = document.getElementById('status');
        if (statusElement) {
            statusElement.textContent = `ERROR: ${message}`;
        }
    }

    // Function to initialize Supabase
    async function initializeSupabase() {
        try {
            console.log('=== Initializing Supabase ===');

            // Check if Supabase is loaded
            if (typeof window.supabase === 'undefined') {
                console.error('❌ Supabase not found in window object');
                throw new Error('Supabase library not loaded properly');
            }

            if (typeof window.supabase.createClient !== 'function') {
                console.error('❌ supabase.createClient is not a function');
                throw new Error('Supabase client creation method not available');
            }

            // Initialize Supabase client with new project credentials
            const SUPABASE_URL = 'https://tqetghhuxpqogxzbwqwe.supabase.co';
            const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRxZXRnaGh1eHBxb2d4emJ3cXdlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYxOTE0NywiZXhwIjoyMDY0MTk1MTQ3fQ.XlT3knuZpj4Q5FMwNU9iGgRhh7krifp90OwfCrJ4PCo';

            console.log('🔑 Supabase URL:', SUPABASE_URL);
            console.log('🔑 Supabase Key:', SUPABASE_KEY ? '*** (key present)' : '❌ Missing key');

            console.log('🔄 Creating Supabase client...');

            // Create the client using the global supabase object
            supabaseClient = window.supabase.createClient(SUPABASE_URL, SUPABASE_KEY);

            if (!supabaseClient) {
                throw new Error('Client creation returned undefined');
            }

            console.log('✅ Supabase client created successfully');

            // Test the connection with a simple query
            console.log('🔍 Testing database connection...');
            try {
                const { data, error } = await supabaseClient
                    .from('products')
                    .select('count', { count: 'exact', head: true });

                if (error) {
                    console.warn('⚠️ Database connection test had issues:', error);
                    // Don't fail initialization for connection test issues
                    console.log('✅ Supabase client created, proceeding anyway...');
                } else {
                    console.log('✅ Database connection successful');
                    console.log('📊 Total products in database:', data || 0);
                }
            } catch (testError) {
                console.warn('⚠️ Connection test failed, but proceeding:', testError);
            }

            return true;

        } catch (error) {
            console.error('❌ Supabase initialization failed:', error);
            showError(`Database error: ${error.message}`);
            return false;
        }
    }

    // Initialize the application
    async function initializeApp() {
        console.log('Initializing application...');

        try {
            // Initialize Supabase first
            const supabaseInitialized = await initializeSupabase();

            if (!supabaseInitialized) {
                console.error('Failed to initialize Supabase');
                // Don't show error message since connection might be working but just no products
                return;
            }

            // Check for existing wallet session
            await checkExistingWallet();

            // Load initial products from Supabase
            console.log('Loading products from Supabase...');
            await loadInitialProducts();

        } catch (error) {
            console.error('Failed to initialize app:', error);
            showError(`Initialization failed: ${error.message}`);
        }
    }

    // Function to load all products from Supabase for background system
    async function loadInitialProducts() {
        console.log('Loading all products from Supabase...');
        const statusElement = document.getElementById('status');
        statusElement.textContent = 'CONNECTING TO SUPABASE...';

        try {
            if (!supabaseClient) {
                throw new Error('Supabase client not initialized');
            }

            // Load ALL products from database (no limit)
            const { data: results, error } = await supabaseClient
                .from('products')
                .select('*, categories(name)')
                .order('title', { ascending: true });

            if (error) {
                throw error;
            }

            if (results && results.length > 0) {
                allDatabaseProducts = results.map(product => ({
                    id: product.id,
                    name: product.title,
                    description: product.description,
                    price: product.price,
                    image_url: product.image_url || 'https://via.placeholder.com/200',
                    category: product.categories?.name || 'Uncategorized',
                    in_stock: product.in_stock !== false
                }));

                // Start the background product system
                initializeBackgroundProducts();
                statusElement.textContent = `LOADED ${results.length} PRODUCTS - BACKGROUND SYSTEM ACTIVE`;
                console.log('Successfully loaded products:', results);
            } else {
                statusElement.textContent = 'NO PRODUCTS FOUND IN DATABASE';
            }
        } catch (error) {
            console.error('Error loading initial products:', error);
            showError(`DATABASE ERROR: ${error.message}`);
        }
    }

    // Function to check database schema
    async function checkDatabaseSchema() {
        console.log('Checking database schema...');
        try {
            if (!supabaseClient) {
                throw new Error('Supabase client not initialized');
            }

            // Check if products table exists and get its columns
            const { data: tables, error } = await supabaseClient.rpc('get_table_columns');

            if (error) {
                console.error('Error getting schema:', error);
                // Try alternative method
                const { data: tables } = await supabaseClient
                    .from('pg_tables')
                    .select('tablename')
                    .eq('schemaname', 'public');
                console.log('Tables in database:', tables);
                return;
            }

            console.log('Database schema:', tables);
        } catch (error) {
            console.error('Error checking schema:', error);
        }
    }

    // Function to list all products for debugging
    async function listAllProducts() {
        console.log('Fetching all products from Supabase...');
        try {
            if (!supabaseClient) {
                throw new Error('Supabase client not initialized');
            }

            const { data: products, error } = await supabaseClient
                .from('products')
                .select('*')
                .limit(10);

            if (error) throw error;
            console.log('Products in database:', products);
            return products;
        } catch (error) {
            console.error('Error listing products:', error);
            return [];
        }
    }

    // Initialize the app when the page loads
    document.addEventListener('DOMContentLoaded', initializeApp);


    </script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #E0E0E0;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }

        /* DasWos Header Styles - Exact match from daswos-18 */
        .daswos-header {
            position: sticky;
            top: 0;
            width: 100%;
            background-color: #E0E0E0;
            z-index: 20;
            color: black;
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 8px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .header-center {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .header-title {
            margin: 0;
            font-size: 18px;
            color: #000;
            font-weight: normal;
        }

        .header-btn {
            background-color: #E0E0E0;
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            color: black;
            display: flex;
            align-items: center;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            border-radius: 0;
            margin-right: 8px;
        }

        .header-btn:last-child {
            margin-right: 0;
        }

        .header-btn:hover {
            background-color: #d0d0d0;
        }

        .header-btn svg {
            height: 16px;
            width: 16px;
            margin-right: 4px;
        }

        /* User dropdown styles */
        .user-dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            background-color: #E0E0E0;
            border: 1px solid #d1d5db;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            min-width: 200px;
            z-index: 3000;
        }

        .dropdown-content.show {
            display: block;
        }

        .welcome-message {
            padding: 12px 16px;
            border-bottom: 1px solid #d1d5db;
            background-color: #f5f5f5;
            font-size: 12px;
            color: #000;
            font-weight: bold;
        }

        .dropdown-item {
            display: block;
            width: 100%;
            padding: 8px 16px;
            background: none;
            border: none;
            text-align: left;
            font-size: 12px;
            color: #000;
            cursor: pointer;
            font-family: Arial, sans-serif;
        }

        .dropdown-item:hover {
            background-color: #d0d0d0;
        }

        .ui-overlay {
            position: fixed;
            top: 50px; /* Account for header */
            left: 0;
            width: 100%;
            height: calc(100% - 50px);
            pointer-events: none;
            z-index: 1000;
        }

        .status {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: #666;
            font-size: 14px;
            text-align: center;
            pointer-events: none;
            background-color: rgba(255, 255, 255, 0.8);
            padding: 5px 10px;
            border-radius: 3px;
            border: 1px solid #d1d5db;
        }

        .controls {
            position: fixed;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            pointer-events: all;
            max-width: 90vw;
            z-index: 1001;
        }

        @media (max-height: 600px) {
            .controls {
                bottom: 5px;
                gap: 5px;
            }

            .status {
                top: 10px;
                font-size: 12px;
            }
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
            justify-content: center;
        }

        #commandInput {
            padding: 8px;
            border: 1px solid #d1d5db;
            border-radius: 0;
            background: #fff;
            color: #000;
            font-family: Arial, sans-serif;
            font-size: 12px;
            width: min(250px, 60vw);
        }

        @media (max-width: 480px) {
            #commandInput {
                width: 180px;
                font-size: 11px;
                padding: 6px;
            }

            #executeBtn {
                padding: 6px 12px;
                font-size: 11px;
            }
        }

        #commandInput::placeholder {
            color: #666;
        }

        #executeBtn {
            padding: 8px 16px;
            background-color: #E0E0E0;
            color: #000;
            border: 1px solid #d1d5db;
            border-radius: 0;
            cursor: pointer;
            font-family: Arial, sans-serif;
            font-size: 12px;
        }

        #executeBtn:hover {
            background-color: #d0d0d0;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-top: 8px;
        }

        .control-btn {
            padding: 6px 12px;
            background-color: #E0E0E0;
            color: #000;
            border: 1px solid #d1d5db;
            border-radius: 0;
            cursor: pointer;
            font-family: Arial, sans-serif;
            font-size: 12px;
            transition: background-color 0.2s ease;
            white-space: nowrap;
        }

        @media (max-width: 480px) {
            .control-btn {
                padding: 4px 8px;
                font-size: 10px;
                border-width: 1px;
            }

            .button-grid {
                gap: 5px;
            }
        }

        .control-btn:hover {
            background-color: #d0d0d0;
        }

        .clear-btn {
            background: rgba(231, 76, 60, 0.2) !important;
            border: 2px solid #e74c3c !important;
            color: #e74c3c !important;
        }

        .clear-btn:hover {
            background: #e74c3c !important;
            color: #fff !important;
        }

        .command-suggestions {
            color: #4ecdc4;
            font-size: 10px;
            text-align: center;
            opacity: 0.8;
            max-width: 90vw;
            line-height: 1.2;
        }

        .scale-controls {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
            flex-wrap: wrap;
            justify-content: center;
        }

        @media (max-width: 480px) {
            .command-suggestions {
                font-size: 8px;
                display: none;
            }

            .scale-controls {
                gap: 5px;
            }

            .scale-label {
                font-size: 10px;
            }

            .scale-btn {
                padding: 3px 6px;
                font-size: 9px;
            }
        }

        .scale-label {
            color: #4ecdc4;
            font-size: 12px;
        }

        #scaleSlider {
            width: 100px;
        }

        .scale-btn {
            padding: 5px 8px;
            background: rgba(78, 205, 196, 0.2);
            color: #4ecdc4;
            border: 1px solid #4ecdc4;
            border-radius: 3px;
            cursor: pointer;
            font-size: 10px;
        }

        .scale-btn:hover {
            background: #4ecdc4;
            color: #000;
        }

        #loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #4ecdc4;
            font-size: 18px;
            z-index: 2000;
        }

        @keyframes typewriter {
            0% { width: 0; }
            50% { width: 100%; }
            100% { width: 100%; }
        }

        .product-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #4ecdc4;
            border-radius: 10px;
            padding: 20px;
            color: #4ecdc4;
            font-family: 'Courier New', monospace;
            z-index: 3000;
            max-width: 400px;
            text-align: center;
            display: none;
        }

        .product-popup img {
            max-width: 100%;
            max-height: 200px;
            border-radius: 5px;
            margin-bottom: 10px;
        }

        .product-popup h3 {
            margin: 10px 0;
            color: #fff;
        }

        .product-popup .price {
            font-size: 18px;
            font-weight: bold;
            color: #4ecdc4;
        }

        .product-popup .close-btn {
            background: #4ecdc4;
            color: #000;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
        }

        .search-results-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #ffffff;
            border: 1px solid #ddd;
            border-radius: 8px;
            color: #333;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            z-index: 4000;
            max-width: 80vw;
            max-height: 80vh;
            width: 600px;
            display: none;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .search-popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #ddd;
            background: #f8f9fa;
        }

        .search-popup-header h2 {
            margin: 0;
            color: #333;
            font-size: 18px;
            font-weight: 600;
        }

        .search-popup-header .close-btn {
            background: #dc3545;
            color: #fff;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background-color 0.2s ease;
        }

        .search-popup-header .close-btn:hover {
            background: #c82333;
        }

        .search-results-content {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
            background: #ffffff;
        }

        .search-result-item {
            display: flex;
            gap: 15px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 15px;
            background: #ffffff;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .search-result-item:hover {
            background: #f8f9fa;
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
        }

        .search-result-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
        }

        .search-result-details {
            flex: 1;
        }

        .search-result-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .search-result-description {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .search-result-price {
            font-size: 14px;
            font-weight: 600;
            color: #28a745;
        }

        .search-result-category {
            font-size: 10px;
            color: #999;
            margin-top: 5px;
        }

        .no-results {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }

        /* Removed login modal styles - using standalone login page instead */

        /* Top Right Login/User Area */
        .top-right-auth {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 2000;
            pointer-events: all;
        }

        .login-signup-btn {
            background: rgba(78, 205, 196, 0.9);
            color: #000;
            border: 2px solid #4ecdc4;
            border-radius: 25px;
            padding: 10px 20px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            backdrop-filter: blur(10px);
        }

        .login-signup-btn:hover {
            background: #4ecdc4;
            transform: scale(1.05);
            box-shadow: 0 0 15px rgba(78, 205, 196, 0.5);
        }

        /* User Info Display */
        .user-info {
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #4ecdc4;
            border-radius: 15px;
            padding: 15px;
            color: #4ecdc4;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            display: none;
            backdrop-filter: blur(10px);
            min-width: 150px;
        }

        .user-info .username {
            color: #fff;
            font-weight: bold;
            margin-bottom: 8px;
            text-align: center;
        }

        .user-info .logout-btn {
            background: #e74c3c;
            color: #fff;
            border: none;
            padding: 8px 15px;
            border-radius: 15px;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            font-size: 10px;
            width: 100%;
            transition: all 0.3s ease;
        }

        .user-info .logout-btn:hover {
            background: #c0392b;
            transform: scale(1.05);
        }

        /* AutoShop Modal Styles */
        .autoshop-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 5000;
        }

        .autoshop-form {
            background: #ffffff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 30px;
            color: #333;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .autoshop-form h2 {
            text-align: center;
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
        }

        .autoshop-description {
            text-align: center;
            color: #666;
            margin-bottom: 25px;
            font-size: 14px;
        }

        .autoshop-settings {
            margin-bottom: 25px;
        }

        .autoshop-form .form-group {
            margin-bottom: 20px;
        }

        .autoshop-form label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
            font-size: 14px;
        }

        .autoshop-form input[type="number"],
        .autoshop-form select {
            width: 100%;
            padding: 10px;
            background: #ffffff;
            border: 1px solid #ddd;
            border-radius: 4px;
            color: #333;
            font-family: inherit;
            box-sizing: border-box;
            font-size: 14px;
        }

        .autoshop-form input[type="number"]:focus,
        .autoshop-form select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .duration-inputs {
            display: flex;
            gap: 10px;
        }

        .duration-inputs input {
            flex: 1;
        }

        .duration-inputs select {
            flex: 1;
        }

        .autoshop-form input[type="checkbox"] {
            margin-right: 8px;
        }

        .autoshop-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .autoshop-form .start-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
            font-size: 14px;
            font-weight: 600;
            transition: background-color 0.2s ease;
        }

        .autoshop-form .start-btn:hover {
            background: #218838;
        }

        .autoshop-form .cancel-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
            font-size: 14px;
            font-weight: 600;
            transition: background-color 0.2s ease;
        }

        .autoshop-form .cancel-btn:hover {
            background: #5a6268;
        }

        .autoshop-form .error-message {
            color: #dc3545;
            text-align: center;
            margin-top: 15px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div id="loading">Loading Robot Assets...</div>

    <!-- Product Detail Popup -->
    <div id="productPopup" class="product-popup">
        <img id="popupImage" src="" alt="Product Image">
        <h3 id="popupTitle">Product Title</h3>
        <div id="popupDescription">Product description</div>
        <div id="popupPrice" class="price">$0.00</div>
        <button class="close-btn" onclick="closeProductPopup()">Close</button>
    </div>

    <!-- Search Results Popup -->
    <div id="searchResultsPopup" class="search-results-popup">
        <div class="search-popup-header">
            <h2 id="searchResultsTitle">Search Results</h2>
            <button class="close-btn" onclick="closeSearchResultsPopup()">×</button>
        </div>
        <div id="searchResultsContent" class="search-results-content">
            <!-- Search results will be populated here -->
        </div>
    </div>

    <!-- Removed login modal - using standalone login page instead -->

    <!-- AutoShop Settings Modal -->
    <div id="autoShopModal" class="autoshop-modal" style="display: none;">
        <div class="autoshop-form">
            <h2>🛒 AutoShop Settings</h2>
            <p class="autoshop-description">Configure how the AI will shop for you automatically</p>

            <div class="autoshop-settings">
                <div class="form-group">
                    <label for="maxTotalCoins">Maximum Total Budget (DasWos Coins):</label>
                    <input type="number" id="maxTotalCoins" value="1000" min="100" max="10000">
                </div>

                <div class="form-group">
                    <label for="minItemPrice">Minimum Item Price (DasWos Coins):</label>
                    <input type="number" id="minItemPrice" value="50" min="10" max="1000">
                </div>

                <div class="form-group">
                    <label for="maxItemPrice">Maximum Item Price (DasWos Coins):</label>
                    <input type="number" id="maxItemPrice" value="500" min="50" max="2000">
                </div>

                <div class="form-group">
                    <label for="duration">AutoShop Duration:</label>
                    <div class="duration-inputs">
                        <input type="number" id="durationValue" value="30" min="1" max="1440">
                        <select id="durationUnit">
                            <option value="minutes">Minutes</option>
                            <option value="hours">Hours</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="useRandomMode" checked>
                        Use Random Mode (AI picks any category)
                    </label>
                </div>
            </div>

            <div class="autoshop-buttons">
                <button type="button" class="cancel-btn" onclick="closeAutoShopModal()">Cancel</button>
                <button type="button" class="start-btn" onclick="startAutoShop()">Start AutoShop</button>
            </div>

            <div id="autoShopError" class="error-message"></div>
        </div>
    </div>

    <!-- DasWos Header - Exact match from daswos-18 -->
    <header class="daswos-header">
        <div class="header-container">
            <!-- Home Button -->
            <div class="header-left">
                <button class="header-btn" onclick="window.location.reload()" title="Home">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                    </svg>
                    <span>Home</span>
                </button>
            </div>

            <!-- Center - Empty -->
            <div class="header-center">
            </div>

            <!-- User Section -->
            <div class="header-right">
                <!-- Sell Button -->
                <button class="header-btn" onclick="alert('Sell feature coming soon!')">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                    </svg>
                    <span>Sell</span>
                </button>

                <!-- Shopping Cart Button -->
                <button class="header-btn" onclick="alert('Cart feature coming soon!')">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0L17 18m0 0v2a2 2 0 01-2 2H9a2 2 0 01-2-2v-2m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v9.01"/>
                    </svg>
                    <span>Cart</span>
                </button>

                <!-- Wallet Button -->
                <button class="header-btn" id="walletBtn" onclick="handleWalletClick()">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                    </svg>
                    <span id="walletBtnText">Add Wallet</span>
                </button>
            </div>
        </div>
    </header>

    <div class="ui-overlay">
        <div class="status" id="status">INITIALIZING SYSTEMS...</div>

        <div class="controls">
            <div class="input-container">
                <input type="text" id="commandInput" placeholder="Search products or type: idle, talk, dance, roll..." />
                <button id="executeBtn">Execute</button>
            </div>

            <div class="button-grid">
                <button class="control-btn" id="idleBtn">Idle</button>
                <button class="control-btn" id="talkBtn">Talk</button>
                <button class="control-btn" id="danceBtn">Dance</button>
                <button class="control-btn" id="rollBtn">Roll</button>
                <button class="control-btn" id="searchBtn">Search</button>
                <button class="control-btn" id="centerBtn">Center</button>
                <button class="control-btn clear-btn" id="clearBtn">Refresh Products</button>
                <button class="control-btn" id="walletControlBtn">Wallet</button>
            </div>

            <div class="scale-controls">
                <span class="scale-label">Size:</span>
                <button class="scale-btn" id="scaleDownBtn">-</button>
                <input type="range" id="scaleSlider" min="0.2" max="1.5" step="0.1" value="0.5">
                <button class="scale-btn" id="scaleUpBtn">+</button>
                <button class="scale-btn" id="resetScaleBtn">Reset</button>
                <span class="scale-label" id="scaleValue">50%</span>
            </div>

            <div class="command-suggestions" id="commandSuggestions">
                🎮 Click & drag robot to move • Hold mouse on robot to spin<br>
                💬 Try voice commands: "idle", "talk", "dance", "roll", "search"<br>
                🔍 Search products to see results in popup • Click floating products for details<br>
                🔄 Background products shuffle every 20 seconds • Use "Refresh Products" to restart
            </div>
        </div>
    </div>

    <script>
        // Supabase client is already initialized at the top of the file
        // Product data - NEW ARCHITECTURE
        let allDatabaseProducts = []; // All products from database
        let backgroundProducts = []; // Currently floating background products
        let productShuffleTimer = null; // Timer for shuffling products
        let productImageLoadTimer = 0;

        // Removed user authentication - wallet-only functionality

        // AutoShop variables
        let autoShopActive = false;
        let autoShopSettings = null;
        let autoShopEndTime = null;
        let autoShopTimer = null;
        let autoShopProducts = []; // Products selected by autoshop

        // Image variables for different robot views
        let robotImages = {
            front: null,
            side: null,
            threeQuarter: null,
            back: null,
            top: null
        };

        // Animation state variables
        let robotState = 'idle';
        let previousState = 'idle';
        let stateStartTime = 0;
        let transitionProgress = 0;
        let isTransitioning = false;

        // Position and movement variables
        let robotX, robotY;
        let targetX = 0, targetY = 0;
        let isRolling = false;
        let rollDirection = 0;
        let rollSpeed = 0;

        // Animation effect variables
        let headRotation = 0;
        let headBobAmount = 0;
        let bodyRotation = 0;
        let bodyRotationSpeed = 0;
        let armLeftRotation = 0;
        let armRightRotation = 0;
        let legsVisible = true;
        let legsVisibility = 1;
        let eyeBlinkTime = 0;
        let isBlinking = false;
        let talkPulse = 0;
        let dancePhase = 0;
        let searchAngle = 0;

        // View management variables
        let currentView = 'front';
        let targetView = 'front';
        let viewTransitionProgress = 0;

        // Mouse interaction variables
        let lastMouseX = 0;
        let lastMouseY = 0;
        let mouseInteractionTimer = 0;

        // Mouse hold and spinning variables
        var isMousePressed = false;
        var mouseHoldStartTime = 0;
        var isSpinning = false;
        var spinRotation = 0;
        var spinSpeed = 0;
        const HOLD_THRESHOLD = 300;
        const MAX_SPIN_SPEED = 0.15;

        // Constants
        const TRANSITION_DURATION = 500;
        const VIEW_TRANSITION_DURATION = 300;
        const SHADOW_OPACITY = 0.3;
        const SHADOW_SCALE_Y = 0.2;
        const SHADOW_OFFSET_Y = 20;

        // Scale-related variables
        let robotScale = 0.5;
        let targetScale = 0.5;
        const MIN_SCALE = 0.2;
        const MAX_SCALE = 1.5;
        const DEFAULT_SCALE = 0.5;
        const SCALE_TRANSITION_SPEED = 0.1;

        // Centering and position variables
        let centerX = 0;
        let centerY = 0;
        let shouldReturnToCenter = false;
        const POSITION_TRANSITION_SPEED = 0.05;
        let danceStartX = 0;
        let danceStartY = 0;

        async function preload() {
            console.log('Starting to load robot images...');

            // Load robot images from attached_assets with error handling
            robotImages.front = loadImage('attached_assets/robot_front_view.png',
                () => console.log('Front view loaded'),
                () => console.error('Failed to load front view'));
            robotImages.side = loadImage('attached_assets/robot_side_view.png',
                () => console.log('Side view loaded'),
                () => console.error('Failed to load side view'));
            robotImages.threeQuarter = loadImage('attached_assets/robot_three_quarter_view.png',
                () => console.log('Three quarter view loaded'),
                () => console.error('Failed to load three quarter view'));
            robotImages.back = loadImage('attached_assets/robot_back_view.png',
                () => console.log('Back view loaded'),
                () => console.error('Failed to load back view'));
            robotImages.top = loadImage('attached_assets/robot_top_view.png',
                () => console.log('Top view loaded'),
                () => console.error('Failed to load top view'));

            // Note: Supabase initialization is handled in the main app initialization
        }







        function closeProductPopup() {
            document.getElementById('productPopup').style.display = 'none';
        }

        function showProductPopup(product) {
            const popup = document.getElementById('productPopup');
            const image = document.getElementById('popupImage');
            const title = document.getElementById('popupTitle');
            const description = document.getElementById('popupDescription');
            const price = document.getElementById('popupPrice');

            // Set product details
            title.textContent = product.name || product.title;
            description.textContent = product.description;
            price.textContent = `$${product.price}`;

            // Handle image loading with fallback
            if (product.image_url) {
                image.src = product.image_url;
                image.onerror = function() {
                    console.warn('Failed to load product image, using placeholder');
                    this.src = 'https://via.placeholder.com/200x200/4a90e2/ffffff?text=' + encodeURIComponent(product.name || product.title);
                };
            } else {
                image.src = 'https://via.placeholder.com/200x200/4a90e2/ffffff?text=' + encodeURIComponent(product.name || product.title);
            }

            popup.style.display = 'block';
        }

        // NEW SEARCH POPUP FUNCTIONS

        function showSearchResultsPopup(searchTerm, results, errorMessage = null) {
            const popup = document.getElementById('searchResultsPopup');
            const title = document.getElementById('searchResultsTitle');
            const content = document.getElementById('searchResultsContent');

            title.textContent = `Search Results for "${searchTerm}"`;

            if (errorMessage) {
                content.innerHTML = `
                    <div class="no-results">
                        <h3>Search Error</h3>
                        <p>${errorMessage}</p>
                    </div>
                `;
            } else if (results.length === 0) {
                content.innerHTML = `
                    <div class="no-results">
                        <h3>No Results Found</h3>
                        <p>No products found matching "${searchTerm}"</p>
                        <p>Try different keywords or check spelling</p>
                    </div>
                `;
            } else {
                let resultsHtml = '';
                results.forEach(product => {
                    const imageUrl = product.image_url || 'https://via.placeholder.com/80x80/4a90e2/ffffff?text=No+Image';
                    const price = product.price ? `$${product.price.toFixed(2)}` : 'Price not available';
                    const category = product.categories?.name || 'Uncategorized';
                    const description = product.description || 'No description available';

                    resultsHtml += `
                        <div class="search-result-item" onclick="showProductDetailsFromSearch(${JSON.stringify(product).replace(/"/g, '&quot;')})">
                            <img src="${imageUrl}" alt="${product.title}" class="search-result-image"
                                 onerror="this.src='https://via.placeholder.com/80x80/4a90e2/ffffff?text=No+Image'">
                            <div class="search-result-details">
                                <div class="search-result-title">${product.title}</div>
                                <div class="search-result-description">${description.substring(0, 100)}${description.length > 100 ? '...' : ''}</div>
                                <div class="search-result-price">${price}</div>
                                <div class="search-result-category">Category: ${category}</div>
                            </div>
                        </div>
                    `;
                });
                content.innerHTML = resultsHtml;
            }

            popup.style.display = 'block';
        }

        function closeSearchResultsPopup() {
            const popup = document.getElementById('searchResultsPopup');
            popup.style.display = 'none';
        }

        function showProductDetailsFromSearch(product) {
            // Close search popup first
            closeSearchResultsPopup();

            // Show product details in the existing product popup
            const productData = {
                id: product.id,
                name: product.title,
                description: product.description,
                price: product.price,
                image_url: product.image_url,
                category: product.categories?.name || 'Uncategorized',
                in_stock: product.in_stock !== false
            };

            showProductPopup(productData);
        }

        // WALLET FUNCTIONALITY

        let currentWallet = null;

        function handleWalletClick() {
            if (currentWallet) {
                // User has wallet, show wallet info
                showWalletInfo();
            } else {
                // User doesn't have wallet, redirect to wallet login
                window.location.href = 'wallet-login.html';
            }
        }

        function showWalletInfo() {
            alert(`Wallet Balance: ${currentWallet.balance} DasWos Coins\nWallet ID: ${currentWallet.wallet_id}`);
        }

        function updateWalletInterface() {
            const walletBtnText = document.getElementById('walletBtnText');

            if (currentWallet) {
                walletBtnText.textContent = 'Wallet';
            } else {
                walletBtnText.textContent = 'Add Wallet';
            }
        }

        // Scrypt password hashing (same as daswos-18)
        async function hashPassword(password) {
            try {
                // Generate random salt (16 bytes)
                const salt = new Uint8Array(16);
                crypto.getRandomValues(salt);

                // Convert password to bytes
                const passwordBytes = new TextEncoder().encode(password);

                // Use scrypt to hash the password
                const hashedBytes = await scrypt(passwordBytes, salt, 16384, 8, 1, 64);

                // Convert to hex and combine with salt
                const hashedHex = Array.from(hashedBytes).map(b => b.toString(16).padStart(2, '0')).join('');
                const saltHex = Array.from(salt).map(b => b.toString(16).padStart(2, '0')).join('');

                return `${hashedHex}.${saltHex}`;
            } catch (error) {
                console.error('Error hashing password:', error);
                throw new Error('Password hashing failed');
            }
        }

        // Check for existing wallet session on page load
        async function checkExistingWallet() {
            try {
                const storedWallet = localStorage.getItem('daswos_wallet');

                if (storedWallet) {
                    currentWallet = JSON.parse(storedWallet);
                    updateWalletInterface();
                    console.log('Restored wallet session:', currentWallet.wallet_id);
                }
            } catch (error) {
                console.error('Wallet check error:', error);
            }
        }

        function logoutWallet() {
            // Clear stored wallet data
            localStorage.removeItem('daswos_wallet');

            // Reset current wallet
            currentWallet = null;

            // Update UI
            updateWalletInterface();

            console.log('Wallet logged out');
        }

        // Add dropdown toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const userMenuBtn = document.getElementById('userMenuBtn');
            const dropdownContent = document.getElementById('dropdownContent');

            if (userMenuBtn && dropdownContent) {
                userMenuBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    dropdownContent.classList.toggle('show');
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function() {
                    dropdownContent.classList.remove('show');
                });

                // Prevent dropdown from closing when clicking inside it
                dropdownContent.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }
        });

        // Removed all login functions - wallet-only functionality

        // NEW BACKGROUND PRODUCT SYSTEM

        // Initialize the background product system
        function initializeBackgroundProducts() {
            console.log('🎨 Initializing background product system...');

            if (!allDatabaseProducts || allDatabaseProducts.length === 0) {
                console.warn('No products available for background system');
                return;
            }

            // Load initial set of random products (max 10)
            loadRandomBackgroundProducts(Math.min(10, allDatabaseProducts.length));

            // Start the shuffle timer (every 20 seconds)
            startProductShuffleTimer();

            console.log('✅ Background product system initialized');
        }

        // Load random products for background animation
        function loadRandomBackgroundProducts(count) {
            console.log(`🎨 Loading ${count} random background products...`);

            // Get random products that aren't already displayed
            const availableProducts = allDatabaseProducts.filter(p =>
                !backgroundProducts.some(bp => bp.product.id === p.id)
            );

            if (availableProducts.length === 0) {
                console.log('No more unique products available');
                return;
            }

            // Shuffle and take the requested count
            const shuffled = [...availableProducts].sort(() => Math.random() - 0.5);
            const selectedProducts = shuffled.slice(0, Math.min(count, availableProducts.length));

            selectedProducts.forEach(product => {
                const bgProduct = createBackgroundProduct(product);
                backgroundProducts.push(bgProduct);
            });

            console.log(`✅ Added ${selectedProducts.length} background products`);
        }

        // Create a background product object
        function createBackgroundProduct(product) {
            const bgProduct = {
                x: random(width * 0.1, width * 0.9),
                y: random(height * 0.1, height * 0.9),
                size: random(60, 120),
                velocityX: random(-1, 1),
                velocityY: random(-1, 1),
                rotation: 0,
                rotationSpeed: random(-0.02, 0.02),
                opacity: random(0.4, 0.7),
                floatSpeed: random(0.5, 2),
                floatOffset: random(0, TWO_PI),
                clickable: true,
                product: product,
                image: null,
                imageLoaded: false,
                imageError: false,
                productId: product.id
            };

            // Load the product image
            if (product.image_url) {
                console.log('Loading background product image:', product.name, 'ID:', product.id);
                bgProduct.image = loadImage(
                    product.image_url,
                    () => {
                        bgProduct.imageLoaded = true;
                        console.log('✅ Background product image loaded:', product.name);
                    },
                    () => {
                        bgProduct.imageError = true;
                        console.error('❌ Failed to load background product image:', product.image_url);
                    }
                );
            } else {
                bgProduct.imageError = true;
            }

            return bgProduct;
        }

        // Start the product shuffle timer
        function startProductShuffleTimer() {
            if (productShuffleTimer) {
                clearInterval(productShuffleTimer);
            }

            productShuffleTimer = setInterval(() => {
                shuffleBackgroundProducts();
            }, 20000); // 20 seconds

            console.log('⏰ Product shuffle timer started (20 second intervals)');
        }

        // Shuffle 2 products every 20 seconds
        function shuffleBackgroundProducts() {
            if (backgroundProducts.length < 2) return;

            console.log('🔄 Shuffling 2 background products...');

            // Remove 2 random products
            const indicesToRemove = [];
            while (indicesToRemove.length < 2 && indicesToRemove.length < backgroundProducts.length) {
                const randomIndex = Math.floor(Math.random() * backgroundProducts.length);
                if (!indicesToRemove.includes(randomIndex)) {
                    indicesToRemove.push(randomIndex);
                }
            }

            // Remove products (sort indices in descending order to avoid index shifting)
            indicesToRemove.sort((a, b) => b - a);
            indicesToRemove.forEach(index => {
                const removed = backgroundProducts.splice(index, 1)[0];
                console.log(`🗑️ Removed product: ${removed.product.name}`);
            });

            // Add 2 new random products
            loadRandomBackgroundProducts(2);
        }

        // Function to clear background products and restart the system
        function clearSearchResults() {
            console.log('🧹 Clearing background products...');

            // Clear background products only
            backgroundProducts = [];

            // Stop the shuffle timer
            if (productShuffleTimer) {
                clearInterval(productShuffleTimer);
                productShuffleTimer = null;
            }

            // Restart the background system
            if (allDatabaseProducts && allDatabaseProducts.length > 0) {
                initializeBackgroundProducts();
                document.getElementById('status').textContent = 'BACKGROUND PRODUCTS REFRESHED';
            } else {
                document.getElementById('status').textContent = 'NO PRODUCTS AVAILABLE FOR BACKGROUND';
            }

            console.log('✅ Background products cleared and restarted');
        }

        // Clear button is now always visible as "Refresh Products"

        function updateImageLoadingStatus() {
            if (backgroundProducts.length === 0) return;

            const loaded = backgroundProducts.filter(p => p.imageLoaded).length;
            const failed = backgroundProducts.filter(p => p.imageError).length;
            const total = backgroundProducts.length;
            const loading = total - loaded - failed;
            const totalProducts = allDatabaseProducts.length;

            if (loading > 0) {
                document.getElementById('status').textContent = `LOADING PRODUCT IMAGES... ${loaded}/${total} LOADED (${totalProducts} TOTAL PRODUCTS)`;
            } else {
                const maxFloating = 20;
                const displayCount = Math.min(total, maxFloating);
                document.getElementById('status').textContent = `DISPLAYING ${displayCount}/${totalProducts} PRODUCTS - ${loaded} LOADED, ${failed} FAILED - READY TO INTERACT`;
            }
        }

        function updateBackgroundProducts() {
            for (let i = 0; i < backgroundProducts.length; i++) {
                let product = backgroundProducts[i];

                // Update floating animation with gentle movement
                product.x += product.velocityX;
                product.y += product.velocityY;
                product.y += sin(millis() * 0.001 * product.floatSpeed + product.floatOffset) * 0.3;
                product.rotation += product.rotationSpeed;

                // Bounce off edges instead of wrapping
                if (product.x < product.size/2 || product.x > width - product.size/2) {
                    product.velocityX *= -1;
                    product.x = constrain(product.x, product.size/2, width - product.size/2);
                }
                if (product.y < product.size/2 || product.y > height - product.size/2) {
                    product.velocityY *= -1;
                    product.y = constrain(product.y, product.size/2, height - product.size/2);
                }
            }
        }

        function drawBackgroundProducts() {
            for (let product of backgroundProducts) {
                push();
                translate(product.x, product.y);
                rotate(product.rotation);

                // Add glow effect
                fill(255, 255, 255, product.opacity * 0.3 * 255);
                noStroke();
                ellipse(0, 0, product.size + 10, product.size + 10);

                // Draw the product image or fallback
                if (product.image && product.imageLoaded && product.image.width > 0) {
                    // Draw the actual product image
                    tint(255, 255, 255, product.opacity * 255);
                    image(product.image, -product.size/2, -product.size/2, product.size, product.size);
                    noTint();
                } else if (product.imageError || !product.product.image_url) {
                    // Draw fallback for failed/missing images
                    fill(100, 150, 255, product.opacity * 255);
                    stroke(255, 255, 255, product.opacity * 255);
                    strokeWeight(2);
                    rect(-product.size/2, -product.size/2, product.size, product.size, 10);

                    // Add product icon
                    fill(255, 255, 255, product.opacity * 255);
                    noStroke();
                    textAlign(CENTER, CENTER);
                    textSize(product.size * 0.3);
                    text('📦', 0, 0);
                } else {
                    // Loading state - show spinner
                    fill(150, 150, 150, product.opacity * 255);
                    stroke(255, 255, 255, product.opacity * 255);
                    strokeWeight(2);
                    ellipse(0, 0, product.size, product.size);

                    // Simple loading animation
                    stroke(100, 200, 255, product.opacity * 255);
                    strokeWeight(4);
                    noFill();
                    let angle = (millis() * 0.01) % TWO_PI;
                    arc(0, 0, product.size * 0.8, product.size * 0.8, angle, angle + PI);
                }

                pop();
            }
        }

        function checkProductClicks(mouseX, mouseY) {
            for (let product of backgroundProducts) {
                let distance = dist(mouseX, mouseY, product.x, product.y);
                if (distance < product.size/2 && product.clickable) {
                    showProductPopup(product.product);
                    return true;
                }
            }
            return false;
        }

        // Function to check if user is interacting with UI elements
        function isUserInteractingWithUI() {
            // Check if any input field is focused
            const activeElement = document.activeElement;
            if (activeElement && (
                activeElement.tagName === 'INPUT' ||
                activeElement.tagName === 'TEXTAREA' ||
                activeElement.tagName === 'SELECT' ||
                activeElement.contentEditable === 'true'
            )) {
                return true;
            }

            // Check if mouse is over UI controls area (top portion of screen)
            // The UI overlay is positioned at the top, so check if mouse is in top area
            if (mouseY < 200) { // Top 200 pixels contain the UI
                return true;
            }

            // Check if any popup is open
            const productPopup = document.getElementById('productPopup');
            if (productPopup && productPopup.style.display !== 'none') {
                return true;
            }

            return false;
        }

        function setup() {
            createCanvas(windowWidth, windowHeight);

            // Ensure canvas doesn't interfere with controls on small screens
            if (windowHeight < 600) {
                createCanvas(windowWidth, windowHeight - 120);
            }

            // Check if images loaded properly with a delay to allow loading
            setTimeout(() => {
                let imagesLoaded = true;
                for (let view in robotImages) {
                    if (!robotImages[view] || robotImages[view].width === 0) {
                        console.error(`Failed to load robot image: ${view}`);
                        imagesLoaded = false;
                    } else {
                        console.log(`${view} view loaded successfully - ${robotImages[view].width}x${robotImages[view].height}`);
                    }
                }

                if (!imagesLoaded) {
                    console.error('Some robot images failed to load. Check file paths and server.');
                } else {
                    console.log('All robot images loaded successfully!');
                }
            }, 1000);

            centerX = width / 2;
            centerY = height * 0.35; // Move robot higher up to avoid search bar
            robotX = centerX;
            robotY = centerY;

            // Set up button event listeners
            document.getElementById('idleBtn').addEventListener('click', () => setRobotState('idle'));
            document.getElementById('talkBtn').addEventListener('click', () => setRobotState('talk'));
            document.getElementById('danceBtn').addEventListener('click', () => setRobotState('dance'));
            document.getElementById('rollBtn').addEventListener('click', () => {
                targetX = random(width * 0.2, width * 0.8);
                targetY = random(height * 0.2, height * 0.8);
                setRobotState('roll');
            });
            document.getElementById('searchBtn').addEventListener('click', () => setRobotState('search'));
            document.getElementById('centerBtn').addEventListener('click', () => centerRobot());
            document.getElementById('clearBtn').addEventListener('click', () => clearSearchResults());
            document.getElementById('walletControlBtn').addEventListener('click', () => handleWalletClick());

            // Removed modal login form event listener - using standalone login page

            // Set up scale control event listeners
            document.getElementById('scaleSlider').addEventListener('input', (e) => {
                setRobotScale(parseFloat(e.target.value));
            });
            document.getElementById('scaleUpBtn').addEventListener('click', () => {
                setRobotScale(Math.min(MAX_SCALE, targetScale + 0.1));
            });
            document.getElementById('scaleDownBtn').addEventListener('click', () => {
                setRobotScale(Math.max(MIN_SCALE, targetScale - 0.1));
            });
            document.getElementById('resetScaleBtn').addEventListener('click', () => {
                setRobotScale(DEFAULT_SCALE);
            });

            // Command input handling
            const commandInput = document.getElementById('commandInput');
            const executeBtn = document.getElementById('executeBtn');

            function executeCommand() {
                const command = commandInput.value.toLowerCase().trim();
                const status = document.getElementById('status');

                const commands = {
                    'idle': () => {
                        setRobotState('idle');
                        status.textContent = 'IDLE MODE ACTIVATED';
                    },
                    'talk': () => {
                        setRobotState('talk');
                        status.textContent = 'COMMUNICATION PROTOCOL ENGAGED';
                    },
                    'dance': () => {
                        setRobotState('dance');
                        status.textContent = 'DANCE SEQUENCE INITIATED';
                    },
                    'roll': () => {
                        targetX = random(width * 0.2, width * 0.8);
                        targetY = random(height * 0.2, height * 0.8);
                        setRobotState('roll');
                        status.textContent = 'LOCOMOTION PROTOCOL ACTIVE';
                    },
                    'search': () => {
                        setRobotState('search');
                        status.textContent = 'SEARCH MODE ENABLED';
                    },
                    'center': () => {
                        centerRobot();
                        status.textContent = 'RETURNING TO CENTER POSITION';
                    }
                };

                if (commands[command]) {
                    commands[command]();
                    commandInput.value = '';
                } else if (command === 'lets autoshop' || command === 'let\'s autoshop' || command === 'autoshop') {
                    // Show AutoShop settings modal
                    showAutoShopModal();
                    commandInput.value = '';
                    status.textContent = 'AUTOSHOP SETTINGS OPENED';
                } else if (command) {
                    // Treat as search query - NEW: Show results in popup
                    console.log('Searching for:', command);
                    const statusElement = document.getElementById('status');
                    statusElement.textContent = `SEARCHING SUPABASE FOR "${command.toUpperCase()}"`;

                    // Make the search function async
                    (async () => {
                        try {
                            if (!supabaseClient) {
                                throw new Error('Supabase client not initialized');
                            }

                            // Now perform the search
                            console.log('Executing search query...');
                            const { data: results, error } = await supabaseClient
                                .from('products')
                                .select('*, categories (name)')
                                .or(`title.ilike.%${command}%,description.ilike.%${command}%`)
                                .order('title', { ascending: true })
                                .limit(20);

                            console.log('Search results:', { results, error });

                            if (error) throw error;

                            // Show search results in popup (not floating products)
                            showSearchResultsPopup(command, results || []);
                            statusElement.textContent = `SEARCH COMPLETE - ${results?.length || 0} RESULTS FOUND`;

                        } catch (error) {
                            console.error('Search error:', error);
                            statusElement.textContent = `SEARCH ERROR: ${error.message}`;
                            showSearchResultsPopup(command, [], error.message);
                        }
                    })();

                    commandInput.value = '';
                }
            }

            executeBtn.addEventListener('click', executeCommand);
            commandInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    executeCommand();
                }
            });

            // Hide loading message
            document.getElementById('loading').style.display = 'none';

            // Initial entrance animation
            robotX = -100;
            targetX = centerX;
            targetY = centerY;
            setRobotState('roll');

            // Welcome message
            setTimeout(() => {
                document.getElementById('status').textContent = 'SYSTEMS ONLINE - INTERACTION READY';
            }, 3000);

            // Hide command suggestions after 10 seconds
            setTimeout(() => {
                const commandSuggestions = document.getElementById('commandSuggestions');
                if (commandSuggestions) {
                    commandSuggestions.style.display = 'none';
                }
            }, 10000);
        }

        function draw() {
            // Clear background with grey gradient effect (DasWos-18 style)
            for (let i = 0; i <= height; i++) {
                let inter = map(i, 0, height, 0, 1);
                let c = lerpColor(color(240, 240, 240), color(220, 220, 220), inter);
                stroke(c);
                line(0, i, width, i);
            }

            updateAnimation();
            updateBackgroundProducts();
            drawBackgroundProducts();
            drawRobot();
            handleMouseInteraction();
        }

        function updateAnimation() {
            let currentTime = millis();
            let timeInState = currentTime - stateStartTime;

            // Handle smooth scale transitions
            if (abs(robotScale - targetScale) > 0.01) {
                robotScale = lerp(robotScale, targetScale, SCALE_TRANSITION_SPEED);
            } else {
                robotScale = targetScale;
            }

            // Handle smooth return to center when needed
            if (shouldReturnToCenter && !isRolling) {
                let distanceToCenter = dist(robotX, robotY, centerX, centerY);
                if (distanceToCenter > 2) {
                    robotX = lerp(robotX, centerX, POSITION_TRANSITION_SPEED);
                    robotY = lerp(robotY, centerY, POSITION_TRANSITION_SPEED);
                } else {
                    robotX = centerX;
                    robotY = centerY;
                    shouldReturnToCenter = false;
                }
            }

            // Handle mouse hold spinning
            if (isMousePressed) {
                let holdDuration = currentTime - mouseHoldStartTime;
                if (holdDuration > HOLD_THRESHOLD && !isSpinning) {
                    isSpinning = true;
                    spinSpeed = 0;
                }

                if (isSpinning) {
                    spinSpeed = min(spinSpeed + 0.005, MAX_SPIN_SPEED);
                    spinRotation += spinSpeed;
                }
            } else if (isSpinning) {
                spinSpeed = max(spinSpeed - 0.01, 0);
                spinRotation += spinSpeed;

                if (spinSpeed <= 0) {
                    isSpinning = false;
                    spinRotation = 0;
                }
            }

            // Handle rolling movement
            if (isRolling) {
                let dx = targetX - robotX;
                let dy = targetY - robotY;
                let distance = sqrt(dx*dx + dy*dy);

                if (distance < 5) {
                    isRolling = false;
                    if (robotState === 'roll') {
                        setRobotState('idle');
                    }
                } else {
                    rollDirection = atan2(dy, dx);
                    rollSpeed = min(distance * 0.05, 5);
                    robotX += cos(rollDirection) * rollSpeed;
                    robotY += sin(rollDirection) * rollSpeed;

                    bodyRotationSpeed = rollSpeed * 0.2;
                    bodyRotation += bodyRotationSpeed;

                    if (abs(cos(rollDirection)) > abs(sin(rollDirection))) {
                        targetView = 'side';
                    } else {
                        targetView = sin(rollDirection) > 0 ? 'threeQuarter' : 'back';
                    }

                    legsVisible = false;
                    legsVisibility = max(0, legsVisibility - 0.1);
                }
            } else if (robotState !== 'roll' && !legsVisible) {
                legsVisible = true;
                legsVisibility = min(1, legsVisibility + 0.05);
            }

            // Handle view transitions
            if (currentView !== targetView) {
                viewTransitionProgress = min(1, timeInState / VIEW_TRANSITION_DURATION);
                if (viewTransitionProgress >= 1) {
                    currentView = targetView;
                    viewTransitionProgress = 0;
                }
            }

            // State-specific updates
            switch (robotState) {
                case 'idle':
                    headBobAmount = sin(currentTime * 0.002) * 5;
                    headRotation = sin(currentTime * 0.001) * 0.1;
                    armLeftRotation = sin(currentTime * 0.001) * 0.05;
                    armRightRotation = sin(currentTime * 0.001 + PI) * 0.05;

                    if (currentTime > eyeBlinkTime && !isBlinking) {
                        isBlinking = true;
                        eyeBlinkTime = currentTime + 200;
                    } else if (currentTime > eyeBlinkTime && isBlinking) {
                        isBlinking = false;
                        eyeBlinkTime = currentTime + random(2000, 5000);
                    }

                    if (!isRolling) targetView = 'front';
                    legsVisible = true;
                    legsVisibility = min(1, legsVisibility + 0.05);
                    break;

                case 'talk':
                    talkPulse = sin(currentTime * 0.01) * 0.05;
                    headBobAmount = sin(currentTime * 0.01) * 3;
                    armLeftRotation = sin(currentTime * 0.008) * 0.2;
                    armRightRotation = sin(currentTime * 0.008 + PI) * 0.2;
                    targetView = 'front';
                    legsVisible = true;
                    legsVisibility = min(1, legsVisibility + 0.05);
                    break;

                case 'dance':
                    dancePhase += 0.05;
                    headBobAmount = sin(dancePhase * 2) * 8;
                    headRotation = sin(dancePhase) * 0.2;
                    armLeftRotation = sin(dancePhase) * 0.4;
                    armRightRotation = sin(dancePhase + PI) * 0.4;

                    if (!isRolling) {
                        robotX = danceStartX + sin(dancePhase) * 30;
                        robotY = danceStartY + sin(dancePhase * 0.5) * 10;
                    }

                    if (timeInState % 2000 < 500) {
                        targetView = 'front';
                    } else if (timeInState % 2000 < 1000) {
                        targetView = 'threeQuarter';
                    } else if (timeInState % 2000 < 1500) {
                        targetView = 'side';
                    } else {
                        targetView = 'threeQuarter';
                    }

                    legsVisible = true;
                    legsVisibility = min(1, legsVisibility + 0.05);
                    break;

                case 'search':
                    searchAngle += 0.03;
                    headRotation = sin(searchAngle) * 0.3;
                    headBobAmount = sin(currentTime * 0.005) * 3;
                    armLeftRotation = sin(searchAngle * 0.5) * 0.2;
                    armRightRotation = sin(searchAngle * 0.5 + PI) * 0.2;

                    if (timeInState % 3000 < 1000) {
                        targetView = 'front';
                    } else if (timeInState % 3000 < 2000) {
                        targetView = 'threeQuarter';
                    } else {
                        targetView = 'side';
                    }

                    legsVisible = true;
                    legsVisibility = min(1, legsVisibility + 0.05);
                    break;

                case 'roll':
                    headBobAmount = sin(currentTime * 0.01) * 3;
                    armLeftRotation = sin(currentTime * 0.01) * 0.1 * (rollSpeed * 0.1);
                    armRightRotation = sin(currentTime * 0.01 + PI) * 0.1 * (rollSpeed * 0.1);
                    legsVisible = false;
                    legsVisibility = max(0, legsVisibility - 0.1);
                    break;
            }
        }

        function drawRobot() {
            push();
            translate(robotX, robotY);

            // Draw shadow
            drawShadow();

            // Apply bobbing effect
            translate(0, headBobAmount);

            // Scale the robot
            scale(robotScale);

            // Apply spin rotation if spinning
            if (isSpinning || spinSpeed > 0) {
                rotate(spinRotation);
            }

            // Apply body rotation for wheel effect
            if (robotState === 'roll') {
                push();
                rotate(bodyRotation);
            }

            // Determine which image to draw
            let currentImage = robotImages[currentView];

            // Fallback if image didn't load
            if (!currentImage || currentImage.width === 0) {
                // Draw a more detailed fallback robot
                fill(200);
                stroke(255);
                strokeWeight(2);

                // Head
                rect(-25, -40, 50, 40, 5);
                fill(255, 100, 100);
                ellipse(-10, -25, 8, 8); // Left eye
                ellipse(10, -25, 8, 8);  // Right eye

                // Body
                fill(150);
                rect(-30, -5, 60, 50, 5);

                // Screen
                fill(50, 50, 50);
                rect(-20, 5, 40, 25, 3);
                fill(0, 255, 0);
                ellipse(-10, 17, 4, 4);
                ellipse(0, 17, 4, 4);
                ellipse(10, 17, 4, 4);

                // Arms
                fill(100);
                ellipse(-40, 10, 12, 30);
                ellipse(40, 10, 12, 30);

                // Legs/wheels
                if (legsVisible) {
                    fill(200);
                    rect(-15, 45, 10, 15, 3);
                    rect(5, 45, 10, 15, 3);
                }

                // Error message
                fill(255, 0, 0);
                textAlign(CENTER);
                textSize(8);
                text("IMAGE LOAD ERROR", 0, 70);
                return;
            }

            // If transitioning between views, blend them
            if (currentView !== targetView && viewTransitionProgress > 0) {
                tint(255, 255, 255, 255 * (1 - viewTransitionProgress));
                image(currentImage, -currentImage.width/2, -currentImage.height/2);

                tint(255, 255, 255, 255 * viewTransitionProgress);
                let targetImage = robotImages[targetView];
                if (targetImage && targetImage.width > 0) {
                    image(targetImage, -targetImage.width/2, -targetImage.height/2);
                }

                noTint();
            } else {
                if (robotState === 'talk') {
                    scale(1 + talkPulse);
                }
                image(currentImage, -currentImage.width/2, -currentImage.height/2);
            }



            if (robotState === 'roll') {
                pop();
            }

            pop();
        }

        function drawShadow() {
            push();
            translate(0, SHADOW_OFFSET_Y);
            fill(0, 0, 0, SHADOW_OPACITY * 255);
            noStroke();
            ellipse(0, 0, 120 * robotScale, 30 * robotScale * SHADOW_SCALE_Y);
            pop();
        }

        function handleMouseInteraction() {
            // Don't track mouse when user is interacting with UI
            if (isUserInteractingWithUI()) {
                lastMouseX = mouseX;
                lastMouseY = mouseY;
                return;
            }

            let d = dist(mouseX, mouseY, robotX, robotY);

            if (d < 150 * robotScale && (abs(mouseX - lastMouseX) > 5 || abs(mouseY - lastMouseY) > 5)) {
                let angle = atan2(mouseY - robotY, mouseX - robotX);
                headRotation = lerp(headRotation, angle * 0.2, 0.1);

                if (abs(cos(angle)) > 0.7) {
                    targetView = cos(angle) > 0 ? 'threeQuarter' : 'threeQuarter';
                } else {
                    targetView = sin(angle) > 0 ? 'front' : 'top';
                }

                mouseInteractionTimer = millis() + 1000;
            }

            if (mouseInteractionTimer > 0 && millis() > mouseInteractionTimer) {
                mouseInteractionTimer = 0;
                switch (robotState) {
                    case 'idle':
                        targetView = 'front';
                        break;
                    case 'talk':
                        targetView = 'front';
                        break;
                }
            }

            lastMouseX = mouseX;
            lastMouseY = mouseY;
        }

        function setRobotState(state) {
            previousState = robotState;
            robotState = state;
            stateStartTime = millis();

            switch (state) {
                case 'idle':
                    if (!isRolling) {
                        targetView = 'front';
                    }
                    break;
                case 'talk':
                    targetView = 'front';
                    talkPulse = 0;
                    break;
                case 'dance':
                    dancePhase = 0;
                    danceStartX = robotX;
                    danceStartY = robotY;
                    break;
                case 'search':
                    searchAngle = 0;
                    break;
                case 'roll':
                    isRolling = true;
                    legsVisible = false;
                    break;
            }
        }

        function mousePressed() {
            // Check if user is interacting with UI elements
            if (isUserInteractingWithUI()) {
                return; // Don't handle robot interaction when using UI
            }

            // Check if clicking on a background product first
            if (checkProductClicks(mouseX, mouseY)) {
                return; // Product was clicked, don't handle robot interaction
            }

            isMousePressed = true;
            mouseHoldStartTime = millis();

            let d = dist(mouseX, mouseY, robotX, robotY);
            if (d < 100 * robotScale) {
                setTimeout(() => {
                    if (!isSpinning && !isMousePressed) {
                        headBobAmount = -10;
                        setTimeout(() => { headBobAmount = 0; }, 300);
                    }
                }, HOLD_THRESHOLD + 50);
            }
        }

        function mouseReleased() {
            let holdDuration = millis() - mouseHoldStartTime;
            isMousePressed = false;

            // Check if user is interacting with UI elements
            if (isUserInteractingWithUI()) {
                return; // Don't handle robot interaction when using UI
            }

            if (holdDuration < HOLD_THRESHOLD && !isSpinning) {
                let d = dist(mouseX, mouseY, robotX, robotY);
                if (d >= 100 * robotScale) {
                    targetX = mouseX;
                    targetY = mouseY;
                    setRobotState('roll');
                }
            }
        }

        function windowResized() {
            resizeCanvas(windowWidth, windowHeight);
            centerX = width / 2;
            centerY = height * 0.35; // Keep robot higher up

            // Redistribute background products across new canvas size using deterministic positioning
            for (let product of backgroundProducts) {
                if (product.productId) {
                    // Use product ID to maintain consistent positioning after resize
                    const seed = product.productId;
                    const pseudoRandom = (seed * 9301 + 49297) % 233280;
                    const rnd1 = (pseudoRandom / 233280);
                    const rnd2 = ((pseudoRandom * 2) % 233280) / 233280;

                    product.x = rnd1 * width;
                    product.y = rnd2 * height;
                } else {
                    // Fallback for products without ID
                    product.x = constrain(product.x, product.size/2, width - product.size/2);
                    product.y = constrain(product.y, product.size/2, height - product.size/2);
                }
            }
        }

        function setRobotScale(newScale) {
            targetScale = constrain(newScale, MIN_SCALE, MAX_SCALE);
            updateScaleDisplay();
        }

        function updateScaleDisplay() {
            const scaleSlider = document.getElementById('scaleSlider');
            const scaleValue = document.getElementById('scaleValue');

            if (scaleSlider && scaleValue) {
                scaleSlider.value = targetScale;
                scaleValue.textContent = Math.round(targetScale * 100) + '%';
            }
        }

        function centerRobot() {
            shouldReturnToCenter = true;

            if (robotState === 'dance') {
                danceStartX = centerX;
                danceStartY = centerY;
            }
        }

        // AutoShop Functions
        function showAutoShopModal() {
            document.getElementById('autoShopModal').style.display = 'flex';
        }

        function closeAutoShopModal() {
            document.getElementById('autoShopModal').style.display = 'none';
            document.getElementById('autoShopError').textContent = '';
        }

        function startAutoShop() {
            // Get settings from form
            const maxTotalCoins = parseInt(document.getElementById('maxTotalCoins').value);
            const minItemPrice = parseInt(document.getElementById('minItemPrice').value);
            const maxItemPrice = parseInt(document.getElementById('maxItemPrice').value);
            const durationValue = parseInt(document.getElementById('durationValue').value);
            const durationUnit = document.getElementById('durationUnit').value;
            const useRandomMode = document.getElementById('useRandomMode').checked;

            // Validate settings
            if (minItemPrice >= maxItemPrice) {
                document.getElementById('autoShopError').textContent = 'Minimum price must be less than maximum price';
                return;
            }

            if (maxTotalCoins < maxItemPrice) {
                document.getElementById('autoShopError').textContent = 'Total budget must be at least the maximum item price';
                return;
            }

            // Check if user has enough coins (mock check for now)
            const userCoins = 10000; // Mock user coins
            if (userCoins < minItemPrice) {
                document.getElementById('autoShopError').textContent = 'Insufficient DasWos Coins to start AutoShop';
                return;
            }

            // Store settings
            autoShopSettings = {
                maxTotalCoins,
                minItemPrice,
                maxItemPrice,
                duration: { value: durationValue, unit: durationUnit },
                useRandomMode
            };

            // Calculate end time
            const endTime = new Date();
            if (durationUnit === 'minutes') {
                endTime.setMinutes(endTime.getMinutes() + durationValue);
            } else if (durationUnit === 'hours') {
                endTime.setHours(endTime.getHours() + durationValue);
            }
            autoShopEndTime = endTime;

            // Clear existing floating products
            backgroundProducts = [];

            // Start AutoShop
            autoShopActive = true;
            autoShopProducts = [];

            // Close modal
            closeAutoShopModal();

            // Update status
            document.getElementById('status').textContent = `AUTOSHOP STARTED - DURATION: ${durationValue} ${durationUnit.toUpperCase()}`;

            // Start selecting products (1 per minute)
            selectAutoShopProduct();
            autoShopTimer = setInterval(selectAutoShopProduct, 60000); // 1 minute intervals

            // Set timer to end AutoShop
            setTimeout(finalizeAutoShop, durationValue * (durationUnit === 'hours' ? 3600000 : 60000));
        }

        async function selectAutoShopProduct() {
            if (!autoShopActive) return;

            try {
                // Get random product from database
                const { data: products, error } = await supabaseClient
                    .from('products')
                    .select('*')
                    .gte('price', autoShopSettings.minItemPrice)
                    .lte('price', autoShopSettings.maxItemPrice)
                    .limit(50);

                if (error) throw error;

                if (products && products.length > 0) {
                    // Select random product
                    const randomProduct = products[Math.floor(Math.random() * products.length)];

                    // Add to autoshop products
                    autoShopProducts.push(randomProduct);

                    // Add as floating product
                    const floatingProduct = {
                        productId: randomProduct.id,
                        title: randomProduct.title,
                        price: randomProduct.price,
                        image: randomProduct.image_url,
                        x: random(width * 0.1, width * 0.9),
                        y: random(height * 0.2, height * 0.8),
                        size: 60,
                        opacity: 255,
                        rotation: 0,
                        rotationSpeed: random(-0.02, 0.02),
                        driftX: random(-0.5, 0.5),
                        driftY: random(-0.5, 0.5),
                        isAutoShop: true
                    };

                    backgroundProducts.push(floatingProduct);

                    console.log(`AutoShop selected: ${randomProduct.title} for ${randomProduct.price} coins`);
                    document.getElementById('status').textContent = `AUTOSHOP: SELECTED ${randomProduct.title.toUpperCase()} - ${autoShopProducts.length} ITEMS`;
                }
            } catch (error) {
                console.error('AutoShop product selection error:', error);
            }
        }

        function finalizeAutoShop() {
            if (!autoShopActive) return;

            // Stop the timer
            if (autoShopTimer) {
                clearInterval(autoShopTimer);
                autoShopTimer = null;
            }

            // Calculate total cost
            const totalCost = autoShopProducts.reduce((sum, product) => sum + product.price, 0);

            // Mock purchase (in real app, this would deduct from user's wallet)
            console.log(`AutoShop completed! Purchased ${autoShopProducts.length} items for ${totalCost} DasWos Coins`);

            // Update status
            document.getElementById('status').textContent = `AUTOSHOP COMPLETED - PURCHASED ${autoShopProducts.length} ITEMS FOR ${totalCost} COINS`;

            // Clear floating products
            backgroundProducts = [];

            // Reset AutoShop state
            autoShopActive = false;
            autoShopProducts = [];
            autoShopSettings = null;
            autoShopEndTime = null;

            // Show completion message
            setTimeout(() => {
                document.getElementById('status').textContent = 'SYSTEMS ONLINE - INTERACTION READY';
            }, 5000);
        }
    </script>
</body>
</html>
