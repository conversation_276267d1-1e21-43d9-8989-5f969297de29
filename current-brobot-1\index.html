
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Daswos Robot Animation</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <!-- Load Supabase first with onload handler -->
    <script>
    // Global Supabase client instance - will be initialized after the library loads

    // Function to initialize the application after Supabase is loaded
    function initApp() {
        console.log('Supabase script loaded, initializing app...');
        document.removeEventListener('DOMContentLoaded', initApp);

        // Initialize the app
        initializeApp().catch(error => {
            console.error('Failed to initialize app:', error);
            showError(`Initialization failed: ${error.message}`);
        });
    }

    // Load scripts in order
    function loadScripts() {
        console.log('Loading Supabase...');
        const supabaseScript = document.createElement('script');
        supabaseScript.src = 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2';
        supabaseScript.onload = () => {
            console.log('Supabase loaded, now loading p5.js...');
            const p5Script = document.createElement('script');
            p5Script.src = 'https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js';
            p5Script.onload = () => {
                console.log('All scripts loaded, initializing application...');
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', initApp);
                } else {
                    initApp();
                }
            };
            document.head.appendChild(p5Script);
        };
        document.head.appendChild(supabaseScript);
    }

    // Start loading scripts
    loadScripts();
    </script>

    <!-- Our application code -->
    <script>

    // Global Supabase client - will be set after initialization
    let supabaseClient = null;

    // Function to show error message
    function showError(message) {
        console.error(message);
        const statusElement = document.getElementById('status');
        if (statusElement) {
            statusElement.textContent = `ERROR: ${message}`;
        }
    }

    // Function to initialize Supabase
    async function initializeSupabase() {
        try {
            console.log('=== Initializing Supabase ===');

            // Check if Supabase is loaded
            if (typeof window.supabase === 'undefined') {
                console.error('❌ Supabase not found in window object');
                throw new Error('Supabase library not loaded properly');
            }

            if (typeof window.supabase.createClient !== 'function') {
                console.error('❌ supabase.createClient is not a function');
                throw new Error('Supabase client creation method not available');
            }

            // Initialize Supabase client with hardcoded values
            const SUPABASE_URL = 'https://nzgajyfnijjfsvdmidqd.supabase.co';
            const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56Z2FqeWZuaWpqZnN2ZG1pZHFkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYwMDUzNSwiZXhwIjoyMDY0MTc2NTM1fQ.XK2clfmvi5JI5NhQuCrz-WChn-ZvRW9VY2e3gVtNwK4';

            console.log('🔑 Supabase URL:', SUPABASE_URL);
            console.log('🔑 Supabase Key:', SUPABASE_KEY ? '*** (key present)' : '❌ Missing key');

            console.log('🔄 Creating Supabase client...');

            // Create the client using the global supabase object
            supabaseClient = window.supabase.createClient(SUPABASE_URL, SUPABASE_KEY);

            if (!supabaseClient) {
                throw new Error('Client creation returned undefined');
            }

            console.log('✅ Supabase client created successfully');

            // Test the connection with a simple query
            console.log('🔍 Testing database connection...');
            const { data, error } = await supabaseClient
                .from('products')
                .select('count', { count: 'exact', head: true });

            if (error) {
                console.error('❌ Database connection test failed:', error);
                throw new Error(`Database connection failed: ${error.message}`);
            }

            console.log('✅ Database connection successful');
            console.log('📊 Total products in database:', data[0]?.count || 0);

            return true;

        } catch (error) {
            console.error('❌ Supabase initialization failed:', error);
            showError(`Database error: ${error.message}`);
            return false;
        }
    }

    // Initialize the application
    async function initializeApp() {
        console.log('Initializing application...');

        try {
            // Initialize Supabase first
            const supabaseInitialized = await initializeSupabase();

            if (!supabaseInitialized) {
                console.error('Failed to initialize Supabase');
                showError('Failed to connect to the database. Please check your connection and refresh the page.');
                return;
            }

            // Load initial products from Supabase
            console.log('Loading products from Supabase...');
            await loadInitialProducts();

        } catch (error) {
            console.error('Failed to initialize app:', error);
            showError(`Initialization failed: ${error.message}`);
        }
    }

    // Function to load initial products from Supabase
    async function loadInitialProducts() {
        console.log('Loading initial products from Supabase...');
        const statusElement = document.getElementById('status');
        statusElement.textContent = 'CONNECTING TO SUPABASE...';

        try {
            if (!supabaseClient) {
                throw new Error('Supabase client not initialized');
            }

            const { data: results, error } = await supabaseClient
                .from('products')
                .select('*, categories(name)')
                .order('title', { ascending: true })
                .limit(20);

            if (error) {
                throw error;
            }

            if (results && results.length > 0) {
                allProducts = results.map(product => ({
                    id: product.id,
                    name: product.title,
                    description: product.description,
                    price: product.price,
                    image_url: product.image_url || 'https://via.placeholder.com/200',
                    category: product.categories?.name || 'Uncategorized',
                    in_stock: product.in_stock !== false
                }));
                currentProducts = allProducts.slice(0, 15);
                loadBackgroundProducts();
                statusElement.textContent = `LOADED ${results.length} PRODUCTS FROM SUPABASE`;
                console.log('Successfully loaded products:', results);
            } else {
                statusElement.textContent = 'NO PRODUCTS FOUND IN DATABASE';
            }
        } catch (error) {
            console.error('Error loading initial products:', error);
            showError(`DATABASE ERROR: ${error.message}`);
        }
    }

    // Function to check database schema
    async function checkDatabaseSchema() {
        console.log('Checking database schema...');
        try {
            if (!supabaseClient) {
                throw new Error('Supabase client not initialized');
            }

            // Check if products table exists and get its columns
            const { data: tables, error } = await supabaseClient.rpc('get_table_columns');

            if (error) {
                console.error('Error getting schema:', error);
                // Try alternative method
                const { data: tables } = await supabaseClient
                    .from('pg_tables')
                    .select('tablename')
                    .eq('schemaname', 'public');
                console.log('Tables in database:', tables);
                return;
            }

            console.log('Database schema:', tables);
        } catch (error) {
            console.error('Error checking schema:', error);
        }
    }

    // Function to list all products for debugging
    async function listAllProducts() {
        console.log('Fetching all products from Supabase...');
        try {
            if (!supabaseClient) {
                throw new Error('Supabase client not initialized');
            }

            const { data: products, error } = await supabaseClient
                .from('products')
                .select('*')
                .limit(10);

            if (error) throw error;
            console.log('Products in database:', products);
            return products;
        } catch (error) {
            console.error('Error listing products:', error);
            return [];
        }
    }

    // Initialize the app when the page loads
    document.addEventListener('DOMContentLoaded', initializeApp);


    </script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            font-family: 'Courier New', monospace;
            overflow: hidden;
        }

        .ui-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1000;
        }

        .title {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: #fff;
            font-size: 28px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            animation: glow 2s ease-in-out infinite alternate;
            pointer-events: none;
        }

        @keyframes glow {
            from { text-shadow: 2px 2px 4px rgba(0,0,0,0.5), 0 0 10px #4ecdc4; }
            to { text-shadow: 2px 2px 4px rgba(0,0,0,0.5), 0 0 20px #4ecdc4, 0 0 30px #4ecdc4; }
        }

        .status {
            position: absolute;
            top: 70px;
            left: 50%;
            transform: translateX(-50%);
            color: #4ecdc4;
            font-size: 16px;
            text-align: center;
            pointer-events: none;
            animation: typewriter 4s steps(20) infinite;
            overflow: hidden;
            white-space: nowrap;
            border-right: 2px solid #4ecdc4;
        }

        .controls {
            position: fixed;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            pointer-events: all;
            max-width: 90vw;
            z-index: 1001;
        }

        @media (max-height: 600px) {
            .controls {
                bottom: 5px;
                gap: 5px;
            }

            .title {
                font-size: 20px;
                top: 10px;
            }

            .status {
                top: 40px;
                font-size: 12px;
            }
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
            justify-content: center;
        }

        #commandInput {
            padding: 8px;
            border: 2px solid #4ecdc4;
            border-radius: 5px;
            background: rgba(0,0,0,0.8);
            color: #4ecdc4;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            width: min(250px, 60vw);
        }

        @media (max-width: 480px) {
            #commandInput {
                width: 180px;
                font-size: 11px;
                padding: 6px;
            }

            #executeBtn {
                padding: 6px 12px;
                font-size: 11px;
            }
        }

        #commandInput::placeholder {
            color: #666;
        }

        #executeBtn {
            padding: 10px 20px;
            background: #4ecdc4;
            color: #000;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }

        #executeBtn:hover {
            background: #45b7aa;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-top: 8px;
        }

        .control-btn {
            padding: 6px 10px;
            background: rgba(78, 205, 196, 0.2);
            color: #4ecdc4;
            border: 2px solid #4ecdc4;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        @media (max-width: 480px) {
            .control-btn {
                padding: 4px 8px;
                font-size: 10px;
                border-width: 1px;
            }

            .button-grid {
                gap: 5px;
            }
        }

        .control-btn:hover {
            background: #4ecdc4;
            color: #000;
        }

        .command-suggestions {
            color: #4ecdc4;
            font-size: 10px;
            text-align: center;
            opacity: 0.8;
            max-width: 90vw;
            line-height: 1.2;
        }

        .scale-controls {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
            flex-wrap: wrap;
            justify-content: center;
        }

        @media (max-width: 480px) {
            .command-suggestions {
                font-size: 8px;
                display: none;
            }

            .scale-controls {
                gap: 5px;
            }

            .scale-label {
                font-size: 10px;
            }

            .scale-btn {
                padding: 3px 6px;
                font-size: 9px;
            }
        }

        .scale-label {
            color: #4ecdc4;
            font-size: 12px;
        }

        #scaleSlider {
            width: 100px;
        }

        .scale-btn {
            padding: 5px 8px;
            background: rgba(78, 205, 196, 0.2);
            color: #4ecdc4;
            border: 1px solid #4ecdc4;
            border-radius: 3px;
            cursor: pointer;
            font-size: 10px;
        }

        .scale-btn:hover {
            background: #4ecdc4;
            color: #000;
        }

        #loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #4ecdc4;
            font-size: 18px;
            z-index: 2000;
        }

        @keyframes typewriter {
            0% { width: 0; }
            50% { width: 100%; }
            100% { width: 100%; }
        }

        .product-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #4ecdc4;
            border-radius: 10px;
            padding: 20px;
            color: #4ecdc4;
            font-family: 'Courier New', monospace;
            z-index: 3000;
            max-width: 400px;
            text-align: center;
            display: none;
        }

        .product-popup img {
            max-width: 100%;
            max-height: 200px;
            border-radius: 5px;
            margin-bottom: 10px;
        }

        .product-popup h3 {
            margin: 10px 0;
            color: #fff;
        }

        .product-popup .price {
            font-size: 18px;
            font-weight: bold;
            color: #4ecdc4;
        }

        .product-popup .close-btn {
            background: #4ecdc4;
            color: #000;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div id="loading">Loading Robot Assets...</div>

    <div id="productPopup" class="product-popup">
        <img id="popupImage" src="" alt="Product Image">
        <h3 id="popupTitle">Product Title</h3>
        <div id="popupDescription">Product description</div>
        <div id="popupPrice" class="price">$0.00</div>
        <button class="close-btn" onclick="closeProductPopup()">Close</button>
    </div>

    <div class="ui-overlay">
        <h1 class="title">🤖 ASK DASWOS</h1>
        <div class="status" id="status">INITIALIZING SYSTEMS...</div>

        <div class="controls">
            <div class="input-container">
                <input type="text" id="commandInput" placeholder="Search products or type: idle, talk, dance, roll..." />
                <button id="executeBtn">Execute</button>
            </div>

            <div class="button-grid">
                <button class="control-btn" id="idleBtn">Idle</button>
                <button class="control-btn" id="talkBtn">Talk</button>
                <button class="control-btn" id="danceBtn">Dance</button>
                <button class="control-btn" id="rollBtn">Roll</button>
                <button class="control-btn" id="searchBtn">Search</button>
                <button class="control-btn" id="centerBtn">Center</button>
            </div>

            <div class="scale-controls">
                <span class="scale-label">Size:</span>
                <button class="scale-btn" id="scaleDownBtn">-</button>
                <input type="range" id="scaleSlider" min="0.2" max="1.5" step="0.1" value="0.5">
                <button class="scale-btn" id="scaleUpBtn">+</button>
                <button class="scale-btn" id="resetScaleBtn">Reset</button>
                <span class="scale-label" id="scaleValue">50%</span>
            </div>

            <div class="command-suggestions">
                🎮 Click & drag robot to move • Hold mouse on robot to spin<br>
                💬 Try voice commands: "idle", "talk", "dance", "roll", "search"<br>
                🖱️ Mouse near robot changes views • Search products to display in eyes
            </div>
        </div>
    </div>

    <script>
        // Supabase client is already initialized at the top of the file
        // Product data
        let allProducts = [];
        let currentProducts = [];
        let backgroundProducts = [];
        let productImageLoadTimer = 0;

        // Image variables for different robot views
        let robotImages = {
            front: null,
            side: null,
            threeQuarter: null,
            back: null,
            top: null
        };

        // Animation state variables
        let robotState = 'idle';
        let previousState = 'idle';
        let stateStartTime = 0;
        let transitionProgress = 0;
        let isTransitioning = false;

        // Position and movement variables
        let robotX, robotY;
        let targetX = 0, targetY = 0;
        let isRolling = false;
        let rollDirection = 0;
        let rollSpeed = 0;

        // Animation effect variables
        let headRotation = 0;
        let headBobAmount = 0;
        let bodyRotation = 0;
        let bodyRotationSpeed = 0;
        let armLeftRotation = 0;
        let armRightRotation = 0;
        let legsVisible = true;
        let legsVisibility = 1;
        let eyeBlinkTime = 0;
        let isBlinking = false;
        let talkPulse = 0;
        let dancePhase = 0;
        let searchAngle = 0;

        // View management variables
        let currentView = 'front';
        let targetView = 'front';
        let viewTransitionProgress = 0;

        // Mouse interaction variables
        let lastMouseX = 0;
        let lastMouseY = 0;
        let mouseInteractionTimer = 0;

        // Mouse hold and spinning variables
        var isMousePressed = false;
        var mouseHoldStartTime = 0;
        var isSpinning = false;
        var spinRotation = 0;
        var spinSpeed = 0;
        const HOLD_THRESHOLD = 300;
        const MAX_SPIN_SPEED = 0.15;

        // Constants
        const TRANSITION_DURATION = 500;
        const VIEW_TRANSITION_DURATION = 300;
        const SHADOW_OPACITY = 0.3;
        const SHADOW_SCALE_Y = 0.2;
        const SHADOW_OFFSET_Y = 20;

        // Scale-related variables
        let robotScale = 0.5;
        let targetScale = 0.5;
        const MIN_SCALE = 0.2;
        const MAX_SCALE = 1.5;
        const DEFAULT_SCALE = 0.5;
        const SCALE_TRANSITION_SPEED = 0.1;

        // Centering and position variables
        let centerX = 0;
        let centerY = 0;
        let shouldReturnToCenter = false;
        const POSITION_TRANSITION_SPEED = 0.05;
        let danceStartX = 0;
        let danceStartY = 0;

        async function preload() {
            console.log('Starting to load robot images...');

            // Load robot images from attached_assets with error handling
            robotImages.front = loadImage('attached_assets/robot_front_view.png',
                () => console.log('Front view loaded'),
                () => console.error('Failed to load front view'));
            robotImages.side = loadImage('attached_assets/robot_side_view.png',
                () => console.log('Side view loaded'),
                () => console.error('Failed to load side view'));
            robotImages.threeQuarter = loadImage('attached_assets/robot_three_quarter_view.png',
                () => console.log('Three quarter view loaded'),
                () => console.error('Failed to load three quarter view'));
            robotImages.back = loadImage('attached_assets/robot_back_view.png',
                () => console.log('Back view loaded'),
                () => console.error('Failed to load back view'));
            robotImages.top = loadImage('attached_assets/robot_top_view.png',
                () => console.log('Top view loaded'),
                () => console.error('Failed to load top view'));

            // Note: Supabase initialization is handled in the main app initialization
        }







        function closeProductPopup() {
            document.getElementById('productPopup').style.display = 'none';
        }

        function showProductPopup(product) {
            const popup = document.getElementById('productPopup');
            const image = document.getElementById('popupImage');
            const title = document.getElementById('popupTitle');
            const description = document.getElementById('popupDescription');
            const price = document.getElementById('popupPrice');

            // Set product details
            title.textContent = product.name || product.title;
            description.textContent = product.description;
            price.textContent = `$${product.price}`;

            // Handle image loading with fallback
            if (product.image_url) {
                image.src = product.image_url;
                image.onerror = function() {
                    console.warn('Failed to load product image, using placeholder');
                    this.src = 'https://via.placeholder.com/200x200/4a90e2/ffffff?text=' + encodeURIComponent(product.name || product.title);
                };
            } else {
                image.src = 'https://via.placeholder.com/200x200/4a90e2/ffffff?text=' + encodeURIComponent(product.name || product.title);
            }

            popup.style.display = 'block';
        }

        // Function to load background products for animation
        function loadBackgroundProducts() {
            if (!allProducts || allProducts.length === 0) return;

            backgroundProducts = [];
            const numProducts = Math.min(10, allProducts.length);

            console.log(`🎨 Loading ${numProducts} background product images...`);

            for (let i = 0; i < numProducts; i++) {
                const product = allProducts[Math.floor(Math.random() * allProducts.length)];
                if (product) {
                    const bgProduct = {
                        x: random(width),
                        y: random(height),
                        size: random(50, 150),
                        velocityX: random(-1, 1),
                        velocityY: random(-1, 1),
                        rotation: 0,
                        rotationSpeed: random(-0.02, 0.02),
                        opacity: random(0.3, 0.8),
                        floatSpeed: random(0.5, 2),
                        floatOffset: random(0, TWO_PI),
                        clickable: true,
                        product: product,
                        image: null,
                        imageLoaded: false,
                        imageError: false
                    };

                    // Load the product image
                    if (product.image_url) {
                        console.log('Loading product image:', product.name || product.title, 'from', product.image_url);
                        bgProduct.image = loadImage(
                            product.image_url,
                            () => {
                                bgProduct.imageLoaded = true;
                                console.log('✅ Product image loaded successfully:', product.name || product.title);
                                updateImageLoadingStatus();
                            },
                            () => {
                                bgProduct.imageError = true;
                                console.error('❌ Failed to load product image:', product.image_url, 'for product:', product.name || product.title);
                                updateImageLoadingStatus();
                            }
                        );
                    } else {
                        console.warn('⚠️ No image URL for product:', product.name || product.title);
                        bgProduct.imageError = true;
                    }

                    backgroundProducts.push(bgProduct);
                }
            }

            // Initial status update
            setTimeout(updateImageLoadingStatus, 100);
        }

        function updateImageLoadingStatus() {
            if (backgroundProducts.length === 0) return;

            const loaded = backgroundProducts.filter(p => p.imageLoaded).length;
            const failed = backgroundProducts.filter(p => p.imageError).length;
            const total = backgroundProducts.length;
            const loading = total - loaded - failed;

            if (loading > 0) {
                document.getElementById('status').textContent = `LOADING PRODUCT IMAGES... ${loaded}/${total} LOADED`;
            } else {
                document.getElementById('status').textContent = `PRODUCT IMAGES: ${loaded} LOADED, ${failed} FAILED - READY TO INTERACT`;
            }
        }

        function updateBackgroundProducts() {
            for (let i = 0; i < backgroundProducts.length; i++) {
                let product = backgroundProducts[i];

                // Update floating animation with gentle movement
                product.x += product.velocityX;
                product.y += product.velocityY;
                product.y += sin(millis() * 0.001 * product.floatSpeed + product.floatOffset) * 0.3;
                product.rotation += product.rotationSpeed;

                // Bounce off edges instead of wrapping
                if (product.x < product.size/2 || product.x > width - product.size/2) {
                    product.velocityX *= -1;
                    product.x = constrain(product.x, product.size/2, width - product.size/2);
                }
                if (product.y < product.size/2 || product.y > height - product.size/2) {
                    product.velocityY *= -1;
                    product.y = constrain(product.y, product.size/2, height - product.size/2);
                }
            }
        }

        function drawBackgroundProducts() {
            for (let product of backgroundProducts) {
                push();
                translate(product.x, product.y);
                rotate(product.rotation);

                // Add glow effect
                fill(255, 255, 255, product.opacity * 0.3 * 255);
                noStroke();
                ellipse(0, 0, product.size + 10, product.size + 10);

                // Draw the product image or fallback
                if (product.image && product.imageLoaded && product.image.width > 0) {
                    // Draw the actual product image
                    tint(255, 255, 255, product.opacity * 255);
                    image(product.image, -product.size/2, -product.size/2, product.size, product.size);
                    noTint();
                } else if (product.imageError || !product.product.image_url) {
                    // Draw fallback for failed/missing images
                    fill(100, 150, 255, product.opacity * 255);
                    stroke(255, 255, 255, product.opacity * 255);
                    strokeWeight(2);
                    rect(-product.size/2, -product.size/2, product.size, product.size, 10);

                    // Add product icon
                    fill(255, 255, 255, product.opacity * 255);
                    noStroke();
                    textAlign(CENTER, CENTER);
                    textSize(product.size * 0.3);
                    text('📦', 0, 0);
                } else {
                    // Loading state - show spinner
                    fill(150, 150, 150, product.opacity * 255);
                    stroke(255, 255, 255, product.opacity * 255);
                    strokeWeight(2);
                    ellipse(0, 0, product.size, product.size);

                    // Simple loading animation
                    stroke(100, 200, 255, product.opacity * 255);
                    strokeWeight(4);
                    noFill();
                    let angle = (millis() * 0.01) % TWO_PI;
                    arc(0, 0, product.size * 0.8, product.size * 0.8, angle, angle + PI);
                }

                pop();
            }
        }

        function checkProductClicks(mouseX, mouseY) {
            for (let product of backgroundProducts) {
                let distance = dist(mouseX, mouseY, product.x, product.y);
                if (distance < product.size/2 && product.clickable) {
                    showProductPopup(product.product);
                    return true;
                }
            }
            return false;
        }

        function setup() {
            createCanvas(windowWidth, windowHeight);

            // Ensure canvas doesn't interfere with controls on small screens
            if (windowHeight < 600) {
                createCanvas(windowWidth, windowHeight - 120);
            }

            // Check if images loaded properly with a delay to allow loading
            setTimeout(() => {
                let imagesLoaded = true;
                for (let view in robotImages) {
                    if (!robotImages[view] || robotImages[view].width === 0) {
                        console.error(`Failed to load robot image: ${view}`);
                        imagesLoaded = false;
                    } else {
                        console.log(`${view} view loaded successfully - ${robotImages[view].width}x${robotImages[view].height}`);
                    }
                }

                if (!imagesLoaded) {
                    console.error('Some robot images failed to load. Check file paths and server.');
                } else {
                    console.log('All robot images loaded successfully!');
                }
            }, 1000);

            centerX = width / 2;
            centerY = height / 2;
            robotX = centerX;
            robotY = centerY;

            // Set up button event listeners
            document.getElementById('idleBtn').addEventListener('click', () => setRobotState('idle'));
            document.getElementById('talkBtn').addEventListener('click', () => setRobotState('talk'));
            document.getElementById('danceBtn').addEventListener('click', () => setRobotState('dance'));
            document.getElementById('rollBtn').addEventListener('click', () => {
                targetX = random(width * 0.2, width * 0.8);
                targetY = random(height * 0.2, height * 0.8);
                setRobotState('roll');
            });
            document.getElementById('searchBtn').addEventListener('click', () => setRobotState('search'));
            document.getElementById('centerBtn').addEventListener('click', () => centerRobot());

            // Set up scale control event listeners
            document.getElementById('scaleSlider').addEventListener('input', (e) => {
                setRobotScale(parseFloat(e.target.value));
            });
            document.getElementById('scaleUpBtn').addEventListener('click', () => {
                setRobotScale(Math.min(MAX_SCALE, targetScale + 0.1));
            });
            document.getElementById('scaleDownBtn').addEventListener('click', () => {
                setRobotScale(Math.max(MIN_SCALE, targetScale - 0.1));
            });
            document.getElementById('resetScaleBtn').addEventListener('click', () => {
                setRobotScale(DEFAULT_SCALE);
            });

            // Command input handling
            const commandInput = document.getElementById('commandInput');
            const executeBtn = document.getElementById('executeBtn');

            function executeCommand() {
                const command = commandInput.value.toLowerCase().trim();
                const status = document.getElementById('status');

                const commands = {
                    'idle': () => {
                        setRobotState('idle');
                        status.textContent = 'IDLE MODE ACTIVATED';
                    },
                    'talk': () => {
                        setRobotState('talk');
                        status.textContent = 'COMMUNICATION PROTOCOL ENGAGED';
                    },
                    'dance': () => {
                        setRobotState('dance');
                        status.textContent = 'DANCE SEQUENCE INITIATED';
                    },
                    'roll': () => {
                        targetX = random(width * 0.2, width * 0.8);
                        targetY = random(height * 0.2, height * 0.8);
                        setRobotState('roll');
                        status.textContent = 'LOCOMOTION PROTOCOL ACTIVE';
                    },
                    'search': () => {
                        setRobotState('search');
                        status.textContent = 'SEARCH MODE ENABLED';
                    },
                    'center': () => {
                        centerRobot();
                        status.textContent = 'RETURNING TO CENTER POSITION';
                    }
                };

                if (commands[command]) {
                    commands[command]();
                    commandInput.value = '';
                } else if (command) {
                    // Treat as search query
                    console.log('Searching for:', command);
                    const statusElement = document.getElementById('status');
                    statusElement.textContent = `SEARCHING SUPABASE FOR "${command.toUpperCase()}"`;

                    // Clear previous results
                    allProducts = [];
                    currentProducts = [];
                    updateBackgroundProducts();

                    // Make the search function async
                    (async () => {
                        try {
                            if (!supabaseClient) {
                                throw new Error('Supabase client not initialized');
                            }

                            // Now perform the search
                            console.log('Executing search query...');
                            const { data: results, error } = await supabaseClient
                                .from('products')
                                .select('*, categories (name)')
                                .or(`title.ilike.%${command}%,description.ilike.%${command}%`)
                                .order('title', { ascending: true })
                                .limit(20);

                            console.log('Search results:', { results, error });

                            if (error) throw error;

                            if (results && results.length > 0) {
                                // Update the products list with search results
                                allProducts = results.map(product => ({
                                    id: product.id,
                                    name: product.title,
                                    description: product.description,
                                    price: product.price,
                                    image_url: product.image_url || 'https://via.placeholder.com/200',
                                    category: product.categories?.name || 'Uncategorized',
                                    in_stock: product.in_stock !== false
                                }));
                                currentProducts = allProducts.slice(0, 15);
                                loadBackgroundProducts();
                                statusElement.textContent = `FOUND ${results.length} PRODUCTS MATCHING "${command}"`;
                            } else {
                                statusElement.textContent = `NO PRODUCTS FOUND MATCHING "${command}"`;
                            }
                        } catch (error) {
                            console.error('Search error:', error);
                            showError(`SEARCH ERROR: ${error.message}`);
                        }
                    })();

                    commandInput.value = '';
                }
            }

            executeBtn.addEventListener('click', executeCommand);
            commandInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    executeCommand();
                }
            });

            // Hide loading message
            document.getElementById('loading').style.display = 'none';

            // Initial entrance animation
            robotX = -100;
            targetX = centerX;
            targetY = centerY;
            setRobotState('roll');

            // Welcome message
            setTimeout(() => {
                document.getElementById('status').textContent = 'SYSTEMS ONLINE - INTERACTION READY';
            }, 3000);
        }

        function draw() {
            // Clear background with gradient effect
            for (let i = 0; i <= height; i++) {
                let inter = map(i, 0, height, 0, 1);
                let c = lerpColor(color(30, 60, 114), color(42, 82, 152), inter);
                stroke(c);
                line(0, i, width, i);
            }

            updateAnimation();
            updateBackgroundProducts();
            drawBackgroundProducts();
            drawRobot();
            handleMouseInteraction();
        }

        function updateAnimation() {
            let currentTime = millis();
            let timeInState = currentTime - stateStartTime;

            // Handle smooth scale transitions
            if (abs(robotScale - targetScale) > 0.01) {
                robotScale = lerp(robotScale, targetScale, SCALE_TRANSITION_SPEED);
            } else {
                robotScale = targetScale;
            }

            // Handle smooth return to center when needed
            if (shouldReturnToCenter && !isRolling) {
                let distanceToCenter = dist(robotX, robotY, centerX, centerY);
                if (distanceToCenter > 2) {
                    robotX = lerp(robotX, centerX, POSITION_TRANSITION_SPEED);
                    robotY = lerp(robotY, centerY, POSITION_TRANSITION_SPEED);
                } else {
                    robotX = centerX;
                    robotY = centerY;
                    shouldReturnToCenter = false;
                }
            }

            // Handle mouse hold spinning
            if (isMousePressed) {
                let holdDuration = currentTime - mouseHoldStartTime;
                if (holdDuration > HOLD_THRESHOLD && !isSpinning) {
                    isSpinning = true;
                    spinSpeed = 0;
                }

                if (isSpinning) {
                    spinSpeed = min(spinSpeed + 0.005, MAX_SPIN_SPEED);
                    spinRotation += spinSpeed;
                }
            } else if (isSpinning) {
                spinSpeed = max(spinSpeed - 0.01, 0);
                spinRotation += spinSpeed;

                if (spinSpeed <= 0) {
                    isSpinning = false;
                    spinRotation = 0;
                }
            }

            // Handle rolling movement
            if (isRolling) {
                let dx = targetX - robotX;
                let dy = targetY - robotY;
                let distance = sqrt(dx*dx + dy*dy);

                if (distance < 5) {
                    isRolling = false;
                    if (robotState === 'roll') {
                        setRobotState('idle');
                    }
                } else {
                    rollDirection = atan2(dy, dx);
                    rollSpeed = min(distance * 0.05, 5);
                    robotX += cos(rollDirection) * rollSpeed;
                    robotY += sin(rollDirection) * rollSpeed;

                    bodyRotationSpeed = rollSpeed * 0.2;
                    bodyRotation += bodyRotationSpeed;

                    if (abs(cos(rollDirection)) > abs(sin(rollDirection))) {
                        targetView = 'side';
                    } else {
                        targetView = sin(rollDirection) > 0 ? 'threeQuarter' : 'back';
                    }

                    legsVisible = false;
                    legsVisibility = max(0, legsVisibility - 0.1);
                }
            } else if (robotState !== 'roll' && !legsVisible) {
                legsVisible = true;
                legsVisibility = min(1, legsVisibility + 0.05);
            }

            // Handle view transitions
            if (currentView !== targetView) {
                viewTransitionProgress = min(1, timeInState / VIEW_TRANSITION_DURATION);
                if (viewTransitionProgress >= 1) {
                    currentView = targetView;
                    viewTransitionProgress = 0;
                }
            }

            // State-specific updates
            switch (robotState) {
                case 'idle':
                    headBobAmount = sin(currentTime * 0.002) * 5;
                    headRotation = sin(currentTime * 0.001) * 0.1;
                    armLeftRotation = sin(currentTime * 0.001) * 0.05;
                    armRightRotation = sin(currentTime * 0.001 + PI) * 0.05;

                    if (currentTime > eyeBlinkTime && !isBlinking) {
                        isBlinking = true;
                        eyeBlinkTime = currentTime + 200;
                    } else if (currentTime > eyeBlinkTime && isBlinking) {
                        isBlinking = false;
                        eyeBlinkTime = currentTime + random(2000, 5000);
                    }

                    if (!isRolling) targetView = 'front';
                    legsVisible = true;
                    legsVisibility = min(1, legsVisibility + 0.05);
                    break;

                case 'talk':
                    talkPulse = sin(currentTime * 0.01) * 0.05;
                    headBobAmount = sin(currentTime * 0.01) * 3;
                    armLeftRotation = sin(currentTime * 0.008) * 0.2;
                    armRightRotation = sin(currentTime * 0.008 + PI) * 0.2;
                    targetView = 'front';
                    legsVisible = true;
                    legsVisibility = min(1, legsVisibility + 0.05);
                    break;

                case 'dance':
                    dancePhase += 0.05;
                    headBobAmount = sin(dancePhase * 2) * 8;
                    headRotation = sin(dancePhase) * 0.2;
                    armLeftRotation = sin(dancePhase) * 0.4;
                    armRightRotation = sin(dancePhase + PI) * 0.4;

                    if (!isRolling) {
                        robotX = danceStartX + sin(dancePhase) * 30;
                        robotY = danceStartY + sin(dancePhase * 0.5) * 10;
                    }

                    if (timeInState % 2000 < 500) {
                        targetView = 'front';
                    } else if (timeInState % 2000 < 1000) {
                        targetView = 'threeQuarter';
                    } else if (timeInState % 2000 < 1500) {
                        targetView = 'side';
                    } else {
                        targetView = 'threeQuarter';
                    }

                    legsVisible = true;
                    legsVisibility = min(1, legsVisibility + 0.05);
                    break;

                case 'search':
                    searchAngle += 0.03;
                    headRotation = sin(searchAngle) * 0.3;
                    headBobAmount = sin(currentTime * 0.005) * 3;
                    armLeftRotation = sin(searchAngle * 0.5) * 0.2;
                    armRightRotation = sin(searchAngle * 0.5 + PI) * 0.2;

                    if (timeInState % 3000 < 1000) {
                        targetView = 'front';
                    } else if (timeInState % 3000 < 2000) {
                        targetView = 'threeQuarter';
                    } else {
                        targetView = 'side';
                    }

                    legsVisible = true;
                    legsVisibility = min(1, legsVisibility + 0.05);
                    break;

                case 'roll':
                    headBobAmount = sin(currentTime * 0.01) * 3;
                    armLeftRotation = sin(currentTime * 0.01) * 0.1 * (rollSpeed * 0.1);
                    armRightRotation = sin(currentTime * 0.01 + PI) * 0.1 * (rollSpeed * 0.1);
                    legsVisible = false;
                    legsVisibility = max(0, legsVisibility - 0.1);
                    break;
            }
        }

        function drawRobot() {
            push();
            translate(robotX, robotY);

            // Draw shadow
            drawShadow();

            // Apply bobbing effect
            translate(0, headBobAmount);

            // Scale the robot
            scale(robotScale);

            // Apply spin rotation if spinning
            if (isSpinning || spinSpeed > 0) {
                rotate(spinRotation);
            }

            // Apply body rotation for wheel effect
            if (robotState === 'roll') {
                push();
                rotate(bodyRotation);
            }

            // Determine which image to draw
            let currentImage = robotImages[currentView];

            // Fallback if image didn't load
            if (!currentImage || currentImage.width === 0) {
                // Draw a more detailed fallback robot
                fill(200);
                stroke(255);
                strokeWeight(2);

                // Head
                rect(-25, -40, 50, 40, 5);
                fill(255, 100, 100);
                ellipse(-10, -25, 8, 8); // Left eye
                ellipse(10, -25, 8, 8);  // Right eye

                // Body
                fill(150);
                rect(-30, -5, 60, 50, 5);

                // Screen
                fill(50, 50, 50);
                rect(-20, 5, 40, 25, 3);
                fill(0, 255, 0);
                ellipse(-10, 17, 4, 4);
                ellipse(0, 17, 4, 4);
                ellipse(10, 17, 4, 4);

                // Arms
                fill(100);
                ellipse(-40, 10, 12, 30);
                ellipse(40, 10, 12, 30);

                // Legs/wheels
                if (legsVisible) {
                    fill(200);
                    rect(-15, 45, 10, 15, 3);
                    rect(5, 45, 10, 15, 3);
                }

                // Error message
                fill(255, 0, 0);
                textAlign(CENTER);
                textSize(8);
                text("IMAGE LOAD ERROR", 0, 70);
                return;
            }

            // If transitioning between views, blend them
            if (currentView !== targetView && viewTransitionProgress > 0) {
                tint(255, 255, 255, 255 * (1 - viewTransitionProgress));
                image(currentImage, -currentImage.width/2, -currentImage.height/2);

                tint(255, 255, 255, 255 * viewTransitionProgress);
                let targetImage = robotImages[targetView];
                if (targetImage && targetImage.width > 0) {
                    image(targetImage, -targetImage.width/2, -targetImage.height/2);
                }

                noTint();
            } else {
                if (robotState === 'talk') {
                    scale(1 + talkPulse);
                }
                image(currentImage, -currentImage.width/2, -currentImage.height/2);
            }



            if (robotState === 'roll') {
                pop();
            }

            pop();
        }

        function drawShadow() {
            push();
            translate(0, SHADOW_OFFSET_Y);
            fill(0, 0, 0, SHADOW_OPACITY * 255);
            noStroke();
            ellipse(0, 0, 120 * robotScale, 30 * robotScale * SHADOW_SCALE_Y);
            pop();
        }

        function handleMouseInteraction() {
            let d = dist(mouseX, mouseY, robotX, robotY);

            if (d < 150 * robotScale && (abs(mouseX - lastMouseX) > 5 || abs(mouseY - lastMouseY) > 5)) {
                let angle = atan2(mouseY - robotY, mouseX - robotX);
                headRotation = lerp(headRotation, angle * 0.2, 0.1);

                if (abs(cos(angle)) > 0.7) {
                    targetView = cos(angle) > 0 ? 'threeQuarter' : 'threeQuarter';
                } else {
                    targetView = sin(angle) > 0 ? 'front' : 'top';
                }

                mouseInteractionTimer = millis() + 1000;
            }

            if (mouseInteractionTimer > 0 && millis() > mouseInteractionTimer) {
                mouseInteractionTimer = 0;
                switch (robotState) {
                    case 'idle':
                        targetView = 'front';
                        break;
                    case 'talk':
                        targetView = 'front';
                        break;
                }
            }

            lastMouseX = mouseX;
            lastMouseY = mouseY;
        }

        function setRobotState(state) {
            previousState = robotState;
            robotState = state;
            stateStartTime = millis();

            switch (state) {
                case 'idle':
                    if (!isRolling) {
                        targetView = 'front';
                    }
                    break;
                case 'talk':
                    targetView = 'front';
                    talkPulse = 0;
                    break;
                case 'dance':
                    dancePhase = 0;
                    danceStartX = robotX;
                    danceStartY = robotY;
                    break;
                case 'search':
                    searchAngle = 0;
                    break;
                case 'roll':
                    isRolling = true;
                    legsVisible = false;
                    break;
            }
        }

        function mousePressed() {
            // Check if clicking on a background product first
            if (checkProductClicks(mouseX, mouseY)) {
                return; // Product was clicked, don't handle robot interaction
            }

            isMousePressed = true;
            mouseHoldStartTime = millis();

            let d = dist(mouseX, mouseY, robotX, robotY);
            if (d < 100 * robotScale) {
                setTimeout(() => {
                    if (!isSpinning && !isMousePressed) {
                        headBobAmount = -10;
                        setTimeout(() => { headBobAmount = 0; }, 300);
                    }
                }, HOLD_THRESHOLD + 50);
            }
        }

        function mouseReleased() {
            let holdDuration = millis() - mouseHoldStartTime;
            isMousePressed = false;

            if (holdDuration < HOLD_THRESHOLD && !isSpinning) {
                let d = dist(mouseX, mouseY, robotX, robotY);
                if (d >= 100 * robotScale) {
                    targetX = mouseX;
                    targetY = mouseY;
                    setRobotState('roll');
                }
            }
        }

        function windowResized() {
            resizeCanvas(windowWidth, windowHeight);
            centerX = width / 2;
            centerY = height / 2;

            // Redistribute background products across new canvas size
            for (let product of backgroundProducts) {
                product.x = random(width * 0.1, width * 0.9);
                product.y = random(height * 0.1, height * 0.9);
            }
        }

        function setRobotScale(newScale) {
            targetScale = constrain(newScale, MIN_SCALE, MAX_SCALE);
            updateScaleDisplay();
        }

        function updateScaleDisplay() {
            const scaleSlider = document.getElementById('scaleSlider');
            const scaleValue = document.getElementById('scaleValue');

            if (scaleSlider && scaleValue) {
                scaleSlider.value = targetScale;
                scaleValue.textContent = Math.round(targetScale * 100) + '%';
            }
        }

        function centerRobot() {
            shouldReturnToCenter = true;

            if (robotState === 'dance') {
                danceStartX = centerX;
                danceStartY = centerY;
            }
        }
    </script>
</body>
</html>
