<!DOCTYPE html>
<html>
<head>
    <title>Test Database Connection</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #4ecdc4; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; background: #2a2a2a; }
        button { padding: 10px 20px; margin: 10px; background: #4ecdc4; color: #000; border: none; border-radius: 5px; cursor: pointer; }
        pre { background: #333; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 Database Test</h1>
    <p>Testing connection to your new Supabase database...</p>
    
    <button onclick="testConnection()">Test Connection</button>
    <button onclick="checkTables()">Check Tables</button>
    <button onclick="checkProducts()">Check Products</button>
    <button onclick="checkUsers()">Check Users</button>
    
    <div id="results"></div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        const SUPABASE_URL = 'https://azleuohipwepwkyoafhg.supabase.co';
        const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF6bGV1b2hpcHdlcHdreW9hZmhnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYxMjIxNywiZXhwIjoyMDY0MTg4MjE3fQ.GoMkaJQ8rYq1mBjEPfiH1OpF4mWEyZPtxXgby5XZ-jE';
        
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_KEY);
        
        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'result';
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${typeof content === 'object' ? JSON.stringify(content, null, 2) : content}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }
        
        async function testConnection() {
            try {
                addResult('Connection Test', 'Testing Supabase connection...');
                
                // Simple test query
                const { data, error } = await supabase
                    .from('users')
                    .select('count', { count: 'exact', head: true });
                
                if (error) {
                    addResult('Connection Error', error.message);
                } else {
                    addResult('Connection Success', `Connected! User count: ${data || 0}`);
                }
            } catch (error) {
                addResult('Connection Failed', error.message);
            }
        }
        
        async function checkTables() {
            try {
                addResult('Table Check', 'Checking what tables exist...');
                
                // Try to query each expected table
                const tables = ['users', 'products', 'categories', 'user_sessions', 'cart_items'];
                const results = {};
                
                for (const table of tables) {
                    try {
                        const { data, error } = await supabase
                            .from(table)
                            .select('count', { count: 'exact', head: true });
                        
                        if (error) {
                            results[table] = `Error: ${error.message}`;
                        } else {
                            results[table] = `Exists (${data || 0} records)`;
                        }
                    } catch (e) {
                        results[table] = `Not found: ${e.message}`;
                    }
                }
                
                addResult('Tables Status', results);
            } catch (error) {
                addResult('Table Check Failed', error.message);
            }
        }
        
        async function checkProducts() {
            try {
                addResult('Products Check', 'Checking products table...');
                
                const { data: products, error } = await supabase
                    .from('products')
                    .select('*')
                    .limit(10);
                
                if (error) {
                    addResult('Products Error', error.message);
                } else {
                    addResult('Products Found', `${products.length} products in database:`);
                    if (products.length > 0) {
                        products.forEach(product => {
                            addResult(`Product: ${product.title}`, {
                                id: product.id,
                                title: product.title,
                                price: product.price,
                                seller_name: product.seller_name,
                                in_stock: product.in_stock
                            });
                        });
                    } else {
                        addResult('No Products', 'Database has no products! This is why you see no background products.');
                    }
                }
            } catch (error) {
                addResult('Products Check Failed', error.message);
            }
        }
        
        async function checkUsers() {
            try {
                addResult('Users Check', 'Checking users table...');
                
                const { data: users, error } = await supabase
                    .from('users')
                    .select('username, email, is_admin')
                    .limit(5);
                
                if (error) {
                    addResult('Users Error', error.message);
                } else {
                    addResult('Users Found', users);
                }
            } catch (error) {
                addResult('Users Check Failed', error.message);
            }
        }
    </script>
</body>
</html>
