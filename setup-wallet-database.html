<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Wallet Database</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 24px;
        }
        .logo {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 16px;
        }
        .logo-text {
            font-size: 32px;
            font-weight: bold;
            color: black;
            margin-right: 8px;
        }
        .logo-box {
            width: 32px;
            height: 32px;
            background: black;
            position: relative;
        }
        .logo-box::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 14px;
            height: 10px;
            background: white;
        }
        .logo-box::after {
            content: '';
            position: absolute;
            bottom: 2px;
            left: 2px;
            width: 8px;
            height: 8px;
            background: white;
        }
        .status {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
            font-size: 14px;
        }
        .status.info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            color: #1976d2;
        }
        .status.success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
        .status.error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }
        .button {
            background: black;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            margin: 8px;
        }
        .button:hover {
            background: #333;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e5e5e5;
            border-radius: 6px;
            padding: 16px;
            margin-top: 16px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .config-info {
            background: #f8f9fa;
            border: 1px solid #e5e5e5;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 16px;
        }
        .config-info h3 {
            margin-top: 0;
            color: #333;
        }
        .config-info code {
            background: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <span class="logo-text">daswos</span>
                <div class="logo-box"></div>
            </div>
            <h1>Wallet Database Setup</h1>
            <p>Initialize the separate wallet database for DasWos applications</p>
        </div>

        <div class="config-info">
            <h3>Database Configuration</h3>
            <p><strong>URL:</strong> <code>https://mjyaqqsxhkqyzqufpxzl.supabase.co</code></p>
            <p><strong>Database:</strong> Separate wallet database instance</p>
            <p><strong>Purpose:</strong> Store wallet credentials, balances, and transactions</p>
        </div>

        <div id="status" class="status info">
            Ready to setup wallet database. Click "Setup Database" to begin.
        </div>

        <div style="text-align: center;">
            <button id="setupBtn" class="button" onclick="setupDatabase()">Setup Database</button>
            <button id="testBtn" class="button" onclick="testConnection()" disabled>Test Connection</button>
            <button id="createDemoBtn" class="button" onclick="createDemoWallet()" disabled>Create Demo Wallet</button>
        </div>

        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="wallet_database_config.js"></script>
    <script>
        let supabaseClient = null;
        let logElement = null;

        function log(message) {
            if (!logElement) {
                logElement = document.getElementById('log');
                logElement.style.display = 'block';
            }
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        async function initializeClient() {
            try {
                supabaseClient = supabase.createClient(
                    WALLET_DATABASE_CONFIG.SUPABASE_URL,
                    WALLET_DATABASE_CONFIG.SUPABASE_KEY
                );
                log('Supabase client initialized');
                return true;
            } catch (error) {
                log(`Error initializing client: ${error.message}`);
                return false;
            }
        }

        async function setupDatabase() {
            updateStatus('Setting up wallet database...', 'info');
            document.getElementById('setupBtn').disabled = true;

            try {
                // Initialize client
                if (!await initializeClient()) {
                    throw new Error('Failed to initialize Supabase client');
                }

                log('Starting wallet database setup...');

                // Read the schema file content (we'll embed it directly)
                const schemaSQL = `
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- WALLETS TABLE
CREATE TABLE IF NOT EXISTS wallets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    balance DECIMAL(15, 2) DEFAULT 100.00 NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    is_active BOOLEAN DEFAULT true NOT NULL,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    failed_login_attempts INTEGER DEFAULT 0 NOT NULL,
    locked_until TIMESTAMP WITH TIME ZONE,
    creation_ip INET,
    last_login_ip INET,
    CONSTRAINT wallet_id_length CHECK (char_length(wallet_id) >= 3 AND char_length(wallet_id) <= 50),
    CONSTRAINT balance_non_negative CHECK (balance >= 0)
);

-- WALLET SESSIONS TABLE
CREATE TABLE IF NOT EXISTS wallet_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id UUID NOT NULL REFERENCES wallets(id) ON DELETE CASCADE,
    session_token TEXT NOT NULL UNIQUE,
    device_info JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- TRANSACTIONS TABLE
CREATE TABLE IF NOT EXISTS transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id UUID NOT NULL REFERENCES wallets(id) ON DELETE CASCADE,
    transaction_type TEXT NOT NULL,
    amount DECIMAL(15, 2) NOT NULL,
    balance_before DECIMAL(15, 2) NOT NULL,
    balance_after DECIMAL(15, 2) NOT NULL,
    description TEXT,
    reference_id TEXT,
    reference_type TEXT,
    from_wallet_id UUID REFERENCES wallets(id),
    to_wallet_id UUID REFERENCES wallets(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    ip_address INET,
    user_agent TEXT,
    status TEXT DEFAULT 'completed' NOT NULL,
    CONSTRAINT amount_positive CHECK (amount > 0),
    CONSTRAINT valid_transaction_type CHECK (transaction_type IN ('credit', 'debit', 'purchase', 'refund', 'transfer_in', 'transfer_out', 'admin_adjustment')),
    CONSTRAINT valid_status CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    CONSTRAINT transfer_wallets_different CHECK (from_wallet_id != to_wallet_id OR from_wallet_id IS NULL OR to_wallet_id IS NULL)
);

-- WALLET CONNECTIONS TABLE
CREATE TABLE IF NOT EXISTS wallet_connections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id UUID NOT NULL REFERENCES wallets(id) ON DELETE CASCADE,
    database_name TEXT NOT NULL,
    user_id INTEGER,
    username TEXT,
    connected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    is_primary BOOLEAN DEFAULT false NOT NULL,
    CONSTRAINT valid_database_name CHECK (database_name IN ('daswos-18', 'current-brobot-1')),
    UNIQUE(database_name, user_id, wallet_id)
);

-- INDEXES
CREATE INDEX IF NOT EXISTS idx_wallets_wallet_id ON wallets(wallet_id);
CREATE INDEX IF NOT EXISTS idx_wallets_created_at ON wallets(created_at);
CREATE INDEX IF NOT EXISTS idx_wallets_is_active ON wallets(is_active);
CREATE INDEX IF NOT EXISTS idx_wallet_sessions_wallet_id ON wallet_sessions(wallet_id);
CREATE INDEX IF NOT EXISTS idx_wallet_sessions_session_token ON wallet_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_wallet_sessions_expires_at ON wallet_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_wallet_sessions_is_active ON wallet_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_transactions_wallet_id ON transactions(wallet_id);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_transactions_transaction_type ON transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_wallet_connections_wallet_id ON wallet_connections(wallet_id);
CREATE INDEX IF NOT EXISTS idx_wallet_connections_database_user ON wallet_connections(database_name, user_id);
                `;

                // Execute schema using RPC call
                log('Executing database schema...');
                const { data, error } = await supabaseClient.rpc('exec_sql', { sql: schemaSQL });
                
                if (error) {
                    // If RPC doesn't work, try direct table creation
                    log('RPC method failed, trying direct table creation...');
                    
                    // Create tables one by one
                    const tables = [
                        'CREATE EXTENSION IF NOT EXISTS "uuid-ossp"',
                        // Add individual table creation statements here if needed
                    ];
                    
                    // For now, we'll assume the schema is already created or will be created manually
                    log('Schema setup completed (manual setup may be required)');
                } else {
                    log('Schema executed successfully');
                }

                updateStatus('Database setup completed successfully!', 'success');
                document.getElementById('testBtn').disabled = false;
                document.getElementById('createDemoBtn').disabled = false;

            } catch (error) {
                log(`Setup error: ${error.message}`);
                updateStatus(`Setup failed: ${error.message}`, 'error');
                document.getElementById('setupBtn').disabled = false;
            }
        }

        async function testConnection() {
            updateStatus('Testing database connection...', 'info');
            
            try {
                if (!supabaseClient) {
                    await initializeClient();
                }

                log('Testing connection to wallet database...');
                
                // Test basic connection
                const { data, error } = await supabaseClient
                    .from('wallets')
                    .select('count(*)')
                    .limit(1);

                if (error) {
                    throw error;
                }

                log('Connection test successful');
                updateStatus('Database connection successful!', 'success');

            } catch (error) {
                log(`Connection test failed: ${error.message}`);
                updateStatus(`Connection failed: ${error.message}`, 'error');
            }
        }

        async function createDemoWallet() {
            updateStatus('Creating demo wallet...', 'info');
            
            try {
                if (!supabaseClient) {
                    await initializeClient();
                }

                log('Creating demo wallet...');

                // Hash the demo password
                const passwordHash = await WalletUtils.hashPassword('demo123');

                // Insert demo wallet
                const { data, error } = await supabaseClient
                    .from('wallets')
                    .insert({
                        wallet_id: 'demo-wallet',
                        password_hash: passwordHash,
                        balance: 1000
                    })
                    .select()
                    .single();

                if (error) {
                    if (error.code === '23505') {
                        log('Demo wallet already exists');
                        updateStatus('Demo wallet already exists', 'info');
                        return;
                    }
                    throw error;
                }

                // Create initial transaction
                await supabaseClient
                    .from('transactions')
                    .insert({
                        wallet_id: data.id,
                        transaction_type: 'credit',
                        amount: 1000,
                        balance_before: 0,
                        balance_after: 1000,
                        description: 'Initial wallet funding',
                        reference_type: 'admin_adjustment'
                    });

                log('Demo wallet created successfully');
                updateStatus('Demo wallet created successfully!', 'success');

            } catch (error) {
                log(`Demo wallet creation failed: ${error.message}`);
                updateStatus(`Demo wallet creation failed: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
