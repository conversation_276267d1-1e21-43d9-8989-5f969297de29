{"version": 3, "file": "RealtimeChannel.d.ts", "sourceRoot": "", "sources": ["../../src/RealtimeChannel.ts"], "names": [], "mappings": "AAAA,OAAO,EAAkB,cAAc,EAAE,MAAM,iBAAiB,CAAA;AAChE,OAAO,IAAI,MAAM,YAAY,CAAA;AAC7B,OAAO,KAAK,cAAc,MAAM,kBAAkB,CAAA;AAClD,OAAO,KAAK,MAAM,aAAa,CAAA;AAC/B,OAAO,gBAAgB,EAAE,EACvB,+BAA+B,EAChC,MAAM,oBAAoB,CAAA;AAC3B,OAAO,KAAK,EACV,2BAA2B,EAC3B,4BAA4B,EAC5B,qBAAqB,EACtB,MAAM,oBAAoB,CAAA;AAI3B,oBAAY,sBAAsB,GAAG;IACnC,MAAM,EAAE;QACN;;;WAGG;QACH,SAAS,CAAC,EAAE;YAAE,IAAI,CAAC,EAAE,OAAO,CAAC;YAAC,GAAG,CAAC,EAAE,OAAO,CAAA;SAAE,CAAA;QAC7C;;WAEG;QACH,QAAQ,CAAC,EAAE;YAAE,GAAG,CAAC,EAAE,MAAM,CAAA;SAAE,CAAA;QAC3B;;WAEG;QACH,OAAO,CAAC,EAAE,OAAO,CAAA;KAClB,CAAA;CACF,CAAA;AAED,aAAK,kCAAkC,GAAG;IACxC,MAAM,EAAE,MAAM,CAAA;IACd,KAAK,EAAE,MAAM,CAAA;IACb,gBAAgB,EAAE,MAAM,CAAA;IACxB,MAAM,EAAE,MAAM,EAAE,CAAA;CACjB,CAAA;AAED,oBAAY,6BAA6B,CAAC,CAAC,SAAS;IAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;CAAE,IACxE,kCAAkC,GAAG;IACnC,SAAS,EAAE,GAAG,sCAAsC,CAAC,MAAM,EAAE,CAAA;IAC7D,GAAG,EAAE,CAAC,CAAA;IACN,GAAG,EAAE,EAAE,CAAA;CACR,CAAA;AAEH,oBAAY,6BAA6B,CAAC,CAAC,SAAS;IAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;CAAE,IACxE,kCAAkC,GAAG;IACnC,SAAS,EAAE,GAAG,sCAAsC,CAAC,MAAM,EAAE,CAAA;IAC7D,GAAG,EAAE,CAAC,CAAA;IACN,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,CAAA;CAChB,CAAA;AAEH,oBAAY,6BAA6B,CAAC,CAAC,SAAS;IAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;CAAE,IACxE,kCAAkC,GAAG;IACnC,SAAS,EAAE,GAAG,sCAAsC,CAAC,MAAM,EAAE,CAAA;IAC7D,GAAG,EAAE,EAAE,CAAA;IACP,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,CAAA;CAChB,CAAA;AAEH,oBAAY,8BAA8B,CAAC,CAAC,SAAS;IAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;CAAE,IACvE,6BAA6B,CAAC,CAAC,CAAC,GAChC,6BAA6B,CAAC,CAAC,CAAC,GAChC,6BAA6B,CAAC,CAAC,CAAC,CAAA;AAEpC,oBAAY,6BAA6B,CACvC,CAAC,SAAS,GAAG,sCAAsC,EAAE,IACnD;IACF;;OAEG;IACH,KAAK,EAAE,CAAC,CAAA;IACR;;OAEG;IACH,MAAM,EAAE,MAAM,CAAA;IACd;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAA;IACd;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAA;CAChB,CAAA;AAED,oBAAY,2BAA2B,GAAG,IAAI,GAAG,WAAW,GAAG,OAAO,CAAA;AAEtE,oBAAY,sCAAsC;IAChD,GAAG,MAAM;IACT,MAAM,WAAW;IACjB,MAAM,WAAW;IACjB,MAAM,WAAW;CAClB;AAED,oBAAY,qBAAqB;IAC/B,SAAS,cAAc;IACvB,QAAQ,aAAa;IACrB,gBAAgB,qBAAqB;IACrC,MAAM,WAAW;CAClB;AAED,oBAAY,yBAAyB;IACnC,UAAU,eAAe;IACzB,SAAS,cAAc;IACvB,MAAM,WAAW;IACjB,aAAa,kBAAkB;CAChC;AAED,eAAO,MAAM,uBAAuB,uBAAiB,CAAA;AAWrD;;;;GAIG;AACH,MAAM,CAAC,OAAO,OAAO,eAAe;IAqBhC,oCAAoC;IAC7B,KAAK,EAAE,MAAM;IACb,MAAM,EAAE,sBAAsB;IAC9B,MAAM,EAAE,cAAc;IAvB/B,QAAQ,EAAE;QACR,CAAC,GAAG,EAAE,MAAM,GAAG;YACb,IAAI,EAAE,MAAM,CAAA;YACZ,MAAM,EAAE;gBAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;aAAE,CAAA;YAC9B,QAAQ,EAAE,QAAQ,CAAA;YAClB,EAAE,CAAC,EAAE,MAAM,CAAA;SACZ,EAAE,CAAA;KACJ,CAAK;IACN,OAAO,EAAE,MAAM,CAAA;IACf,KAAK,iBAAwB;IAC7B,UAAU,UAAQ;IAClB,QAAQ,EAAE,IAAI,CAAA;IACd,WAAW,EAAE,KAAK,CAAA;IAClB,UAAU,EAAE,IAAI,EAAE,CAAK;IACvB,QAAQ,EAAE,gBAAgB,CAAA;IAC1B,oBAAoB,EAAE,MAAM,CAAA;IAC5B,QAAQ,EAAE,MAAM,CAAA;IAChB,OAAO,EAAE,OAAO,CAAA;;IAGd,oCAAoC;IAC7B,KAAK,EAAE,MAAM,EACb,MAAM,EAAE,sBAAuC,EAC/C,MAAM,EAAE,cAAc;IA6D/B,sDAAsD;IACtD,SAAS,CACP,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,yBAAyB,EAAE,GAAG,CAAC,EAAE,KAAK,KAAK,IAAI,EACnE,OAAO,SAAe,GACrB,eAAe;IAmGlB,aAAa,CACX,CAAC,SAAS;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,GAAG,EAAE,KAClC,qBAAqB,CAAC,CAAC,CAAC;IAIvB,KAAK,CACT,OAAO,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,EAC/B,IAAI,GAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAO,GAChC,OAAO,CAAC,2BAA2B,CAAC;IAWjC,OAAO,CACX,IAAI,GAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAO,GAChC,OAAO,CAAC,2BAA2B,CAAC;IAUvC;;OAEG;IACH,EAAE,CACA,IAAI,EAAE,GAAG,qBAAqB,CAAC,QAAQ,EAAE,EACzC,MAAM,EAAE;QAAE,KAAK,EAAE,GAAG,+BAA+B,CAAC,IAAI,EAAE,CAAA;KAAE,EAC5D,QAAQ,EAAE,MAAM,IAAI,GACnB,eAAe;IAClB,EAAE,CAAC,CAAC,SAAS;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,EACjC,IAAI,EAAE,GAAG,qBAAqB,CAAC,QAAQ,EAAE,EACzC,MAAM,EAAE;QAAE,KAAK,EAAE,GAAG,+BAA+B,CAAC,IAAI,EAAE,CAAA;KAAE,EAC5D,QAAQ,EAAE,CAAC,OAAO,EAAE,2BAA2B,CAAC,CAAC,CAAC,KAAK,IAAI,GAC1D,eAAe;IAClB,EAAE,CAAC,CAAC,SAAS;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,EACjC,IAAI,EAAE,GAAG,qBAAqB,CAAC,QAAQ,EAAE,EACzC,MAAM,EAAE;QAAE,KAAK,EAAE,GAAG,+BAA+B,CAAC,KAAK,EAAE,CAAA;KAAE,EAC7D,QAAQ,EAAE,CAAC,OAAO,EAAE,4BAA4B,CAAC,CAAC,CAAC,KAAK,IAAI,GAC3D,eAAe;IAClB,EAAE,CAAC,CAAC,SAAS;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,EACjC,IAAI,EAAE,GAAG,qBAAqB,CAAC,gBAAgB,EAAE,EACjD,MAAM,EAAE,6BAA6B,CAAC,GAAG,sCAAsC,CAAC,GAAG,EAAE,CAAC,EACtF,QAAQ,EAAE,CAAC,OAAO,EAAE,8BAA8B,CAAC,CAAC,CAAC,KAAK,IAAI,GAC7D,eAAe;IAClB,EAAE,CAAC,CAAC,SAAS;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,EACjC,IAAI,EAAE,GAAG,qBAAqB,CAAC,gBAAgB,EAAE,EACjD,MAAM,EAAE,6BAA6B,CAAC,GAAG,sCAAsC,CAAC,MAAM,EAAE,CAAC,EACzF,QAAQ,EAAE,CAAC,OAAO,EAAE,6BAA6B,CAAC,CAAC,CAAC,KAAK,IAAI,GAC5D,eAAe;IAClB,EAAE,CAAC,CAAC,SAAS;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,EACjC,IAAI,EAAE,GAAG,qBAAqB,CAAC,gBAAgB,EAAE,EACjD,MAAM,EAAE,6BAA6B,CAAC,GAAG,sCAAsC,CAAC,MAAM,EAAE,CAAC,EACzF,QAAQ,EAAE,CAAC,OAAO,EAAE,6BAA6B,CAAC,CAAC,CAAC,KAAK,IAAI,GAC5D,eAAe;IAClB,EAAE,CAAC,CAAC,SAAS;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,EACjC,IAAI,EAAE,GAAG,qBAAqB,CAAC,gBAAgB,EAAE,EACjD,MAAM,EAAE,6BAA6B,CAAC,GAAG,sCAAsC,CAAC,MAAM,EAAE,CAAC,EACzF,QAAQ,EAAE,CAAC,OAAO,EAAE,6BAA6B,CAAC,CAAC,CAAC,KAAK,IAAI,GAC5D,eAAe;IAClB;;;;;OAKG;IACH,EAAE,CACA,IAAI,EAAE,GAAG,qBAAqB,CAAC,SAAS,EAAE,EAC1C,MAAM,EAAE;QAAE,KAAK,EAAE,MAAM,CAAA;KAAE,EACzB,QAAQ,EAAE,CAAC,OAAO,EAAE;QAClB,IAAI,EAAE,GAAG,qBAAqB,CAAC,SAAS,EAAE,CAAA;QAC1C,KAAK,EAAE,MAAM,CAAA;QACb,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KACnB,KAAK,IAAI,GACT,eAAe;IAClB,EAAE,CAAC,CAAC,SAAS;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,EACjC,IAAI,EAAE,GAAG,qBAAqB,CAAC,SAAS,EAAE,EAC1C,MAAM,EAAE;QAAE,KAAK,EAAE,MAAM,CAAA;KAAE,EACzB,QAAQ,EAAE,CAAC,OAAO,EAAE;QAClB,IAAI,EAAE,GAAG,qBAAqB,CAAC,SAAS,EAAE,CAAA;QAC1C,KAAK,EAAE,MAAM,CAAA;QACb,OAAO,EAAE,CAAC,CAAA;KACX,KAAK,IAAI,GACT,eAAe;IAClB,EAAE,CAAC,CAAC,SAAS;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,EACjC,IAAI,EAAE,GAAG,qBAAqB,CAAC,MAAM,EAAE,EACvC,MAAM,EAAE,EAAE,EACV,QAAQ,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,IAAI,GAC/B,eAAe;IAQlB;;;;;;;;OAQG;IACG,IAAI,CACR,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW,GAAG,UAAU,GAAG,kBAAkB,CAAA;QACnD,KAAK,EAAE,MAAM,CAAA;QACb,OAAO,CAAC,EAAE,GAAG,CAAA;QACb,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KACnB,EACD,IAAI,GAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAO,GAChC,OAAO,CAAC,2BAA2B,CAAC;IAwDvC,iBAAiB,CAAC,OAAO,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,GAAG,IAAI;IAIxD;;;;;;;;OAQG;IACH,WAAW,CAAC,OAAO,SAAe,GAAG,OAAO,CAAC,IAAI,GAAG,WAAW,GAAG,OAAO,CAAC;CAuT3E"}