<!DOCTYPE html>
<html>
<head>
    <title>Fix Product Images</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .product-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .broken-image { border-color: #dc3545; background: #f8d7da; }
        .working-image { border-color: #28a745; background: #d4edda; }
    </style>
</head>
<body>
    <h1>Fix Product Images for Daswos Robot App</h1>
    
    <div id="status" class="status info">Ready to check and fix product images...</div>
    
    <button onclick="checkAllImages()">1. Check All Product Images</button>
    <button onclick="fixBrokenImages()">2. Fix Broken Images</button>
    <button onclick="testImageLoading()">3. Test Image Loading</button>
    
    <div id="results"></div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        const SUPABASE_URL = 'https://nzgajyfnijjfsvdmidqd.supabase.co';
        const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56Z2FqeWZuaWpqZnN2ZG1pZHFkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYwMDUzNSwiZXhwIjoyMDY0MTc2NTM1fQ.XK2clfmvi5JI5NhQuCrz-WChn-ZvRW9VY2e3gVtNwK4';
        
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_KEY);
        
        let brokenProducts = [];
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML += `
                <div class="status ${type}">
                    <h3>${title}</h3>
                    <div>${content}</div>
                </div>
            `;
        }
        
        async function checkImageUrl(url) {
            try {
                const response = await fetch(url, { method: 'HEAD' });
                return response.ok;
            } catch (error) {
                return false;
            }
        }
        
        async function checkAllImages() {
            updateStatus('Checking all product images...', 'info');
            brokenProducts = [];
            
            try {
                const { data: products, error } = await supabase
                    .from('products')
                    .select('*');
                
                if (error) throw error;
                
                let resultsHtml = '<h3>Image Check Results:</h3>';
                let workingCount = 0;
                let brokenCount = 0;
                
                for (const product of products) {
                    const isWorking = await checkImageUrl(product.image_url);
                    
                    if (isWorking) {
                        workingCount++;
                        resultsHtml += `
                            <div class="product-item working-image">
                                <strong>✅ ${product.title}</strong><br>
                                <small>Image URL: ${product.image_url}</small>
                            </div>
                        `;
                    } else {
                        brokenCount++;
                        brokenProducts.push(product);
                        resultsHtml += `
                            <div class="product-item broken-image">
                                <strong>❌ ${product.title}</strong><br>
                                <small>Broken URL: ${product.image_url}</small>
                            </div>
                        `;
                    }
                }
                
                resultsHtml += `<p><strong>Summary: ${workingCount} working, ${brokenCount} broken</strong></p>`;
                
                addResult('Image Check Complete', resultsHtml, brokenCount > 0 ? 'warning' : 'success');
                updateStatus(`Found ${brokenCount} broken images out of ${products.length} total`, brokenCount > 0 ? 'warning' : 'success');
                
            } catch (error) {
                addResult('Image Check Error', error.message, 'error');
                updateStatus('Image check failed', 'error');
            }
        }
        
        async function fixBrokenImages() {
            if (brokenProducts.length === 0) {
                updateStatus('No broken images found. Run "Check All Product Images" first.', 'warning');
                return;
            }
            
            updateStatus('Fixing broken images...', 'info');
            
            // Replacement image URLs for common products
            const imageReplacements = {
                'blender': 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400&h=400&fit=crop',
                'coffee': 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=400&h=400&fit=crop',
                'headphones': 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
                'watch': 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
                'shoes': 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop',
                'yoga': 'https://images.unsplash.com/photo-1576678927484-cc907957088c?w=400&h=400&fit=crop'
            };
            
            let fixedCount = 0;
            let resultsHtml = '<h3>Image Fix Results:</h3>';
            
            try {
                for (const product of brokenProducts) {
                    let newImageUrl = null;
                    
                    // Try to find a replacement based on product title
                    const title = product.title.toLowerCase();
                    for (const [keyword, url] of Object.entries(imageReplacements)) {
                        if (title.includes(keyword)) {
                            newImageUrl = url;
                            break;
                        }
                    }
                    
                    // If no specific replacement found, use a generic placeholder
                    if (!newImageUrl) {
                        newImageUrl = `https://via.placeholder.com/400x400/4a90e2/ffffff?text=${encodeURIComponent(product.title)}`;
                    }
                    
                    // Update the product in the database
                    const { error } = await supabase
                        .from('products')
                        .update({ image_url: newImageUrl })
                        .eq('id', product.id);
                    
                    if (error) {
                        resultsHtml += `
                            <div class="product-item broken-image">
                                <strong>❌ Failed to fix ${product.title}</strong><br>
                                <small>Error: ${error.message}</small>
                            </div>
                        `;
                    } else {
                        fixedCount++;
                        resultsHtml += `
                            <div class="product-item working-image">
                                <strong>✅ Fixed ${product.title}</strong><br>
                                <small>New URL: ${newImageUrl}</small>
                            </div>
                        `;
                    }
                }
                
                resultsHtml += `<p><strong>Fixed ${fixedCount} out of ${brokenProducts.length} broken images</strong></p>`;
                
                addResult('Image Fix Complete', resultsHtml, 'success');
                updateStatus(`Successfully fixed ${fixedCount} broken images`, 'success');
                
                // Clear the broken products list
                brokenProducts = [];
                
            } catch (error) {
                addResult('Image Fix Error', error.message, 'error');
                updateStatus('Image fix failed', 'error');
            }
        }
        
        async function testImageLoading() {
            updateStatus('Testing image loading in browser...', 'info');
            
            try {
                const { data: products, error } = await supabase
                    .from('products')
                    .select('*')
                    .limit(5);
                
                if (error) throw error;
                
                let resultsHtml = '<h3>Image Loading Test:</h3>';
                
                for (const product of products) {
                    resultsHtml += `
                        <div class="product-item">
                            <strong>${product.title}</strong><br>
                            <img src="${product.image_url}" alt="${product.title}" 
                                 style="max-width: 100px; max-height: 100px; margin: 5px 0;"
                                 onload="this.style.border='2px solid green'"
                                 onerror="this.style.border='2px solid red'; this.alt='❌ Failed to load'">
                            <br>
                            <small>${product.image_url}</small>
                        </div>
                    `;
                }
                
                addResult('Image Loading Test', resultsHtml, 'info');
                updateStatus('Image loading test completed', 'success');
                
            } catch (error) {
                addResult('Image Loading Test Error', error.message, 'error');
                updateStatus('Image loading test failed', 'error');
            }
        }
    </script>
</body>
</html>
