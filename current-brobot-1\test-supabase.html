<!DOCTYPE html>
<html>
<head>
    <title>Supabase Connection Test</title>
</head>
<body>
    <h1>Supabase Connection Test</h1>
    <div id="status">Testing...</div>
    <div id="results"></div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        async function testSupabase() {
            const statusDiv = document.getElementById('status');
            const resultsDiv = document.getElementById('results');
            
            try {
                statusDiv.textContent = 'Initializing Supabase client...';
                
                const SUPABASE_URL = 'https://nzgajyfnijjfsvdmidqd.supabase.co';
                const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56Z2FqeWZuaWpqZnN2ZG1pZHFkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYwMDUzNSwiZXhwIjoyMDY0MTc2NTM1fQ.XK2clfmvi5JI5NhQuCrz-WChn-ZvRW9VY2e3gVtNwK4';
                
                const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_KEY);
                
                statusDiv.textContent = 'Testing connection...';
                
                // Test 1: Check if we can connect
                const { data: testData, error: testError } = await supabase
                    .from('products')
                    .select('count', { count: 'exact', head: true });
                
                if (testError) {
                    throw new Error(`Connection test failed: ${testError.message}`);
                }
                
                statusDiv.textContent = 'Connection successful! Fetching data...';
                
                // Test 2: Try to fetch some products
                const { data: products, error: productsError } = await supabase
                    .from('products')
                    .select('*')
                    .limit(5);
                
                if (productsError) {
                    throw new Error(`Products fetch failed: ${productsError.message}`);
                }
                
                // Test 3: Try to fetch categories
                const { data: categories, error: categoriesError } = await supabase
                    .from('categories')
                    .select('*')
                    .limit(5);
                
                statusDiv.textContent = 'Tests completed successfully!';
                
                resultsDiv.innerHTML = `
                    <h2>Results:</h2>
                    <h3>Products (${products?.length || 0} found):</h3>
                    <pre>${JSON.stringify(products, null, 2)}</pre>
                    
                    <h3>Categories (${categories?.length || 0} found):</h3>
                    <pre>${JSON.stringify(categories, null, 2)}</pre>
                    
                    ${categoriesError ? `<p style="color: red;">Categories error: ${categoriesError.message}</p>` : ''}
                `;
                
            } catch (error) {
                statusDiv.textContent = `Error: ${error.message}`;
                resultsDiv.innerHTML = `<pre style="color: red;">${error.stack}</pre>`;
            }
        }
        
        // Run test when page loads
        window.addEventListener('load', testSupabase);
    </script>
</body>
</html>
