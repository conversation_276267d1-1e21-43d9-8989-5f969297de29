# 🚀 Database Setup Instructions

## Problem Solved
The error `constraint "fk_users_family_owner" for relation "users" already exists` means the schema was partially applied. I've fixed this with proper IF NOT EXISTS logic.

## Option 1: Clean Start (Recommended)

### Step 1: Clean Database
```sql
-- Execute this in Supabase SQL Editor:
-- Copy and paste the contents of cleanup_database.sql
```

### Step 2: Apply Unified Schema
```sql
-- Execute this in Supabase SQL Editor:
-- Copy and paste the contents of unified_schema.sql
```

## Option 2: Continue from Current State

If you want to keep what's already created, just run the updated `unified_schema.sql` - it now has proper IF NOT EXISTS logic for constraints.

## Verification Steps

### 1. Check Tables Created
```sql
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;
```

You should see:
- cart_items
- categories  
- daswos_coins_transactions
- information_content
- products
- purchases
- seller_verification
- user_product_content
- user_sessions
- users

### 2. Check Admin User
```sql
SELECT username, email, is_admin 
FROM users 
WHERE username = 'admin';
```

Should return: admin | <EMAIL> | true

### 3. Check Sample Products
```sql
SELECT title, price, seller_name 
FROM products 
LIMIT 5;
```

Should return 5 sample products.

## Test Login

1. Open: http://localhost:8080
2. Click "Login" button
3. Enter:
   - Username: `admin`
   - Password: `admin123`
4. Should see "Welcome, admin" in top-right corner

## Files Created

- `unified_schema.sql` - Complete schema (FIXED)
- `cleanup_database.sql` - Reset database
- `SETUP_INSTRUCTIONS.md` - This file
- `setup-unified-database.html` - Web-based setup tool

## Troubleshooting

### If you get constraint errors:
1. Run `cleanup_database.sql` first
2. Then run `unified_schema.sql`

### If login doesn't work:
1. Check if admin user exists: `SELECT * FROM users WHERE username = 'admin';`
2. Check browser console for errors
3. Verify Supabase connection

### If products don't load:
1. Check if products exist: `SELECT COUNT(*) FROM products;`
2. Check browser console for errors
3. Verify Supabase URL and key in index.html

## Success Indicators

✅ All 10+ tables created  
✅ Admin user exists  
✅ Sample products loaded  
✅ Login works in Current-Brobot-1  
✅ Products display and search works  
✅ Background products float automatically  

## Next Steps

1. Test both DasWos-18 and Current-Brobot-1 apps
2. Add more users and products as needed
3. Customize schema for your specific requirements
4. Deploy to production when ready

---

**The unified schema is now ready for both apps!** 🎉
