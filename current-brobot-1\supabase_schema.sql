-- Create categories table
CREATE TABLE IF NOT EXISTS categories (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create products table
CREATE TABLE IF NOT EXISTS products (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    image_url TEXT,
    category_id INTEGER,
    tags TEXT[],
    in_stock BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_sessions table
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    session_data JSONB DEFAULT '{}'::jsonb
);

-- Create cart_items table
CREATE TABLE IF NOT EXISTS cart_items (
    id SERIAL PRIMARY KEY,
    session_id UUID,
    product_id INTEGER,
    quantity INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions(expires_at);

-- Enable uuid-ossp extension if not exists
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Add foreign keys after tables are created
ALTER TABLE products 
    ADD CONSTRAINT fk_category 
    FOREIGN KEY (category_id) 
    REFERENCES categories(id) 
    ON DELETE SET NULL;

ALTER TABLE cart_items
    ADD CONSTRAINT fk_product
    FOREIGN KEY (product_id)
    REFERENCES products(id)
    ON DELETE CASCADE;

ALTER TABLE cart_items
    ADD CONSTRAINT fk_session
    FOREIGN KEY (session_id)
    REFERENCES user_sessions(id)
    ON DELETE CASCADE;

-- Insert sample categories
INSERT INTO categories (name, slug, description) VALUES
    ('Electronics', 'electronics', 'Electronic devices and accessories'),
    ('Clothing', 'clothing', 'Apparel and fashion items'),
    ('Home & Garden', 'home-garden', 'Home improvement and gardening supplies'),
    ('Sports', 'sports', 'Sports equipment and gear'),
    ('Toys', 'toys', 'Toys and games');

-- Insert sample products
INSERT INTO products (title, description, price, image_url, category_id, tags, in_stock) VALUES
    ('Wireless Headphones', 'High-quality wireless headphones with noise cancellation', 199.99, 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop', 1, '{"electronics", "audio", "wireless"}', true),
    ('Running Shoes', 'Lightweight running shoes for all terrains', 89.99, 'https://images.unsplash.com/photo-**********-7eec264c27ff?w=400&h=400&fit=crop', 2, '{"footwear", "sports", "running"}', true),
    ('Smart Watch', 'Feature-rich smartwatch with health tracking', 249.99, 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop', 1, '{"electronics", "wearables", "fitness"}', true),
    ('Yoga Mat', 'Non-slip yoga mat for all skill levels', 29.99, 'https://images.unsplash.com/photo-1576678927484-cc907957088c?w=400&h=400&fit=crop', 4, '{"fitness", "yoga", "home"}', true),
    ('Blender', 'High-powered blender for smoothies and food prep', 129.99, 'https://images.unsplash.com/photo-**********-f0409ee7e33a?w=400&h=400&fit=crop', 3, '{"kitchen", "appliances"}', true);

-- Create a function to update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to update the updated_at column on products
CREATE TRIGGER update_products_updated_at
BEFORE UPDATE ON products
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create a function to search products
CREATE OR REPLACE FUNCTION search_products(search_term TEXT)
RETURNS SETOF products AS $$
BEGIN
    RETURN QUERY
    SELECT *
    FROM products
    WHERE
        to_tsvector('english', title || ' ' || COALESCE(description, '')) @@ websearch_to_tsquery('english', search_term)
        OR search_term = ANY(tags);
END;
$$ LANGUAGE plpgsql;
