modules = ["nodejs-20", "web"]
run = "python -m http.server 8000"
language = "python3" 
[nix]
channel = "stable-24_05"

[deployment]
run = ["sh", "-c", "python -m http.server 8000"]

[workflows]
runButton = "Run Server"

[[workflows.workflow]]
name = "Run Server"
author = 42970495
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npx http-server -p 8000 -c-1"

[[ports]]
localPort = 8000
externalPort = 3000
